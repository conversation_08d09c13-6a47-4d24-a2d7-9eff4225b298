{% extends 'base/base.html' %}

{% block title %}نقطة البيع - متجر أدوات طب الأسنان{% endblock %}
{% block page_title %}نقطة البيع{% endblock %}
{% block page_description %}إدارة المبيعات والفواتير بكفاءة عالية{% endblock %}

{% block content %}
<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1 text-neon-green">
                            <i class="fas fa-cash-register me-2 icon-neon"></i>
                            العمليات السريعة
                        </h5>
                        <p class="text-muted mb-0">الوصول السريع للوظائف الأساسية</p>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{% url 'pos:new_invoice' %}" class="btn btn-success">
                            <i class="fas fa-plus me-2 icon-neon"></i>
                            فاتورة جديدة
                        </a>
                        <a href="{% url 'pos:customer_list' %}" class="btn btn-primary">
                            <i class="fas fa-users me-2 icon-neon"></i>
                            إدارة العملاء
                        </a>
                        <a href="{% url 'pos:customer_add' %}" class="btn btn-warning">
                            <i class="fas fa-user-plus me-2 icon-neon"></i>
                            عميل جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <p class="text-muted mb-1">فواتير اليوم</p>
                        <h4 class="mb-0 text-neon-green">0</h4>
                    </div>
                    <div class="bg-success rounded-circle p-3">
                        <i class="fas fa-receipt text-white icon-neon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <p class="text-muted mb-1">مبيعات اليوم</p>
                        <h4 class="mb-0 text-neon-blue">0.00 جنيه</h4>
                    </div>
                    <div class="bg-primary rounded-circle p-3">
                        <i class="fas fa-dollar-sign text-white icon-neon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <p class="text-muted mb-1">عملاء جدد</p>
                        <h4 class="mb-0 text-neon-purple">0</h4>
                    </div>
                    <div class="bg-info rounded-circle p-3">
                        <i class="fas fa-users text-white icon-neon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Invoices -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 text-neon-blue">
                        <i class="fas fa-receipt me-2 icon-neon"></i>
                        الفواتير الحديثة
                    </h5>
                    <a href="#" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye me-1"></i>
                        عرض الكل
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="text-center py-5">
                                    <i class="fas fa-receipt text-muted mb-3" style="font-size: 3rem;"></i>
                                    <p class="text-muted mb-0">لا توجد فواتير حديثة</p>
                                    <a href="{% url 'pos:new_invoice' %}" class="btn btn-primary mt-3">
                                        <i class="fas fa-plus me-2"></i>
                                        إنشاء فاتورة جديدة
                                    </a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions & Statistics -->
<div class="row g-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 text-neon-green">
                    <i class="fas fa-bolt me-2 icon-neon"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{% url 'pos:new_invoice' %}" class="btn btn-success text-start">
                        <i class="fas fa-plus me-3 icon-neon"></i>
                        إنشاء فاتورة جديدة
                    </a>
                    <a href="{% url 'pos:customer_add' %}" class="btn btn-primary text-start">
                        <i class="fas fa-user-plus me-3 icon-neon"></i>
                        إضافة عميل جديد
                    </a>
                    <button class="btn btn-warning text-start" onclick="searchInvoice()">
                        <i class="fas fa-search me-3 icon-neon"></i>
                        البحث عن فاتورة
                    </button>
                    <a href="{% url 'pos:customer_list' %}" class="btn btn-info text-start">
                        <i class="fas fa-users me-3 icon-neon"></i>
                        قائمة العملاء
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 text-neon-purple">
                    <i class="fas fa-chart-bar me-2 icon-neon"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                            <span class="text-muted">إجمالي المبيعات الشهرية</span>
                            <span class="fw-bold text-neon-green">0.00 جنيه</span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                            <span class="text-muted">عدد العملاء</span>
                            <span class="fw-bold text-neon-blue">0</span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                            <span class="text-muted">متوسط الفاتورة</span>
                            <span class="fw-bold text-neon-purple">0.00 جنيه</span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center py-2">
                            <span class="text-muted">أعلى مبيعات يومية</span>
                            <span class="fw-bold text-neon-orange">0.00 جنيه</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function searchInvoice() {
        const invoiceNumber = prompt('أدخل رقم الفاتورة للبحث:');
        if (invoiceNumber) {
            // Implement search functionality
            alert('جاري البحث عن الفاتورة رقم: ' + invoiceNumber);
        }
    }
    
    // Auto-refresh statistics every 30 seconds
    setInterval(function() {
        // Implement auto-refresh for statistics
        console.log('Refreshing POS statistics...');
    }, 30000);
</script>
{% endblock %}
