{% extends 'base/base.html' %}

{% block title %}نقطة البيع - متجر أدوات طب الأسنان{% endblock %}
{% block page_title %}نقطة البيع{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Welcome Section -->
    <div class="card rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold mb-2">نقطة البيع</h2>
                <p class="text-gray-400">إدارة المبيعات والفواتير</p>
            </div>
            <div class="flex space-x-4">
                <a href="{% url 'pos:new_invoice' %}" class="bg-green-600 hover:bg-green-700 px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-plus ml-2"></i>
                    فاتورة جديدة
                </a>
                <a href="{% url 'pos:customer_add' %}" class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-user-plus ml-2"></i>
                    عميل جديد
                </a>
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">فواتير اليوم</p>
                    <p class="text-2xl font-bold text-green-400">0</p>
                </div>
                <div class="bg-green-600 p-3 rounded-full">
                    <i class="fas fa-receipt text-xl text-white"></i>
                </div>
            </div>
        </div>
        
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">مبيعات اليوم</p>
                    <p class="text-2xl font-bold text-blue-400">0.00 جنيه</p>
                </div>
                <div class="bg-blue-600 p-3 rounded-full">
                    <i class="fas fa-dollar-sign text-xl text-white"></i>
                </div>
            </div>
        </div>
        
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">عملاء جدد</p>
                    <p class="text-2xl font-bold text-purple-400">0</p>
                </div>
                <div class="bg-purple-600 p-3 rounded-full">
                    <i class="fas fa-users text-xl text-white"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Invoices -->
    <div class="card rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">الفواتير الحديثة</h3>
            <a href="#" class="text-blue-400 hover:text-blue-300 text-sm">عرض الكل</a>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full text-sm">
                <thead>
                    <tr class="border-b border-gray-600">
                        <th class="text-right py-3 px-4">رقم الفاتورة</th>
                        <th class="text-right py-3 px-4">العميل</th>
                        <th class="text-right py-3 px-4">المبلغ</th>
                        <th class="text-right py-3 px-4">التاريخ</th>
                        <th class="text-right py-3 px-4">الحالة</th>
                        <th class="text-right py-3 px-4">إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="6" class="text-center py-8 text-gray-400">
                            <i class="fas fa-receipt text-4xl mb-4"></i>
                            <p>لا توجد فواتير حتى الآن</p>
                            <a href="{% url 'pos:new_invoice' %}" class="text-blue-400 hover:text-blue-300 mt-2 inline-block">
                                إنشاء أول فاتورة
                            </a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <a href="{% url 'pos:new_invoice' %}" class="card rounded-lg p-6 hover:bg-gray-700 transition-colors text-center">
            <i class="fas fa-plus-circle text-3xl text-green-400 mb-3"></i>
            <h4 class="font-medium mb-2">فاتورة جديدة</h4>
            <p class="text-sm text-gray-400">إنشاء فاتورة مبيعات جديدة</p>
        </a>
        
        <a href="{% url 'pos:customer_list' %}" class="card rounded-lg p-6 hover:bg-gray-700 transition-colors text-center">
            <i class="fas fa-users text-3xl text-blue-400 mb-3"></i>
            <h4 class="font-medium mb-2">إدارة العملاء</h4>
            <p class="text-sm text-gray-400">عرض وإدارة بيانات العملاء</p>
        </a>
        
        <a href="#" class="card rounded-lg p-6 hover:bg-gray-700 transition-colors text-center">
            <i class="fas fa-search text-3xl text-purple-400 mb-3"></i>
            <h4 class="font-medium mb-2">البحث عن فاتورة</h4>
            <p class="text-sm text-gray-400">البحث في الفواتير السابقة</p>
        </a>
        
        <a href="#" class="card rounded-lg p-6 hover:bg-gray-700 transition-colors text-center">
            <i class="fas fa-print text-3xl text-orange-400 mb-3"></i>
            <h4 class="font-medium mb-2">طباعة التقارير</h4>
            <p class="text-sm text-gray-400">طباعة تقارير المبيعات</p>
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh every 30 seconds
    setTimeout(function() {
        location.reload();
    }, 30000);
</script>
{% endblock %}
