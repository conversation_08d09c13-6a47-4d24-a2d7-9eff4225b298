from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.db.models import Sum, Count
from django.utils import timezone
from datetime import datetime, timedelta
from .models import Notification, SystemSettings
from products.models import Product
from pos.models import Invoice, Customer
from packages.models import Package

@login_required
def home(request):
    """
    الصفحة الرئيسية للوحة التحكم
    """
    # إحصائيات عامة
    today = timezone.now().date()
    this_month = today.replace(day=1)

    # إحصائيات اليوم
    today_sales = Invoice.objects.filter(
        created_at__date=today,
        status='completed'
    ).aggregate(
        total=Sum('total_amount'),
        count=Count('id')
    )

    # إحصائيات الشهر
    month_sales = Invoice.objects.filter(
        created_at__date__gte=this_month,
        status='completed'
    ).aggregate(
        total=Sum('total_amount'),
        count=Count('id')
    )

    # المنتجات منخفضة المخزون
    low_stock_products = Product.objects.filter(
        quantity_in_stock__lte=models.F('minimum_stock_level'),
        is_active=True
    )[:5]

    # العملاء الجدد هذا الشهر
    new_customers = Customer.objects.filter(
        created_at__date__gte=this_month
    ).count()

    # الإشعارات غير المقروءة
    unread_notifications = Notification.objects.filter(
        user=request.user,
        is_read=False
    )[:5]

    # أفضل المنتجات مبيعاً
    from django.db.models import Sum
    from pos.models import InvoiceItem

    top_products = InvoiceItem.objects.filter(
        invoice__created_at__date__gte=this_month,
        invoice__status='completed',
        product__isnull=False
    ).values(
        'product__name'
    ).annotate(
        total_quantity=Sum('quantity'),
        total_revenue=Sum('total_price')
    ).order_by('-total_quantity')[:5]

    context = {
        'today_sales': today_sales,
        'month_sales': month_sales,
        'low_stock_products': low_stock_products,
        'new_customers': new_customers,
        'unread_notifications': unread_notifications,
        'top_products': top_products,
        'user': request.user,
    }

    return render(request, 'dashboard/home.html', context)

@login_required
def notifications(request):
    """
    صفحة الإشعارات
    """
    notifications = Notification.objects.filter(
        user=request.user
    ).order_by('-created_at')

    context = {
        'notifications': notifications
    }

    return render(request, 'dashboard/notifications.html', context)

@login_required
def mark_notification_read(request, notification_id):
    """
    تحديد الإشعار كمقروء
    """
    notification = get_object_or_404(
        Notification,
        id=notification_id,
        user=request.user
    )
    notification.mark_as_read()

    return redirect('dashboard:notifications')

@login_required
@staff_member_required
def settings(request):
    """
    إعدادات النظام (للمدير فقط)
    """
    settings_obj = SystemSettings.get_settings()

    if request.method == 'POST':
        settings_obj.store_name = request.POST.get('store_name', '')
        settings_obj.store_address = request.POST.get('store_address', '')
        settings_obj.store_phone = request.POST.get('store_phone', '')
        settings_obj.store_email = request.POST.get('store_email', '')
        settings_obj.tax_rate = request.POST.get('tax_rate', 0)
        settings_obj.currency = request.POST.get('currency', 'EGP')
        settings_obj.low_stock_threshold = request.POST.get('low_stock_threshold', 3)
        settings_obj.invoice_footer = request.POST.get('invoice_footer', '')
        settings_obj.updated_by = request.user
        settings_obj.save()

        messages.success(request, 'تم حفظ الإعدادات بنجاح.')
        return redirect('dashboard:settings')

    context = {
        'settings': settings_obj
    }

    return render(request, 'dashboard/settings.html', context)
