# Generated by Django 5.2.4 on 2025-07-31 11:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AcademicYear',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year_number', models.PositiveIntegerField(choices=[(1, 'السنة الأولى'), (2, 'السنة الثانية'), (3, 'السنة الثالثة'), (4, 'السنة الرابعة'), (5, 'السنة الخامسة')], unique=True, verbose_name='رقم السنة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم السنة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'سنة دراسية',
                'verbose_name_plural': 'السنوات الدراسية',
                'ordering': ['year_number'],
            },
        ),
        migrations.CreateModel(
            name='Package',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الحزمة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('total_price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='السعر الإجمالي')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='packages', to='packages.academicyear', verbose_name='السنة الدراسية')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'حزمة',
                'verbose_name_plural': 'الحزم',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PackageItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1, verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='packages.package', verbose_name='الحزمة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر حزمة',
                'verbose_name_plural': 'عناصر الحزم',
                'unique_together': {('package', 'product')},
            },
        ),
    ]
