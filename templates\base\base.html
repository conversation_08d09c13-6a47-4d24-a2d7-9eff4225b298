<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}متجر أدوات طب الأسنان{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        /* Sophos XG Dark Theme Colors */
        :root {
            --primary-bg: #1a1a1a;
            --secondary-bg: #2d2d2d;
            --accent-bg: #3d3d3d;
            --primary-text: #ffffff;
            --secondary-text: #cccccc;
            --accent-color: #00a8ff;
            --success-color: #00d084;
            --warning-color: #ffa500;
            --danger-color: #ff4757;
        }
        
        body {
            background-color: var(--primary-bg);
            color: var(--primary-text);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            background-color: var(--secondary-bg);
            border-right: 1px solid var(--accent-bg);
        }
        
        .nav-item:hover {
            background-color: var(--accent-bg);
        }
        
        .nav-item.active {
            background-color: var(--accent-color);
        }
        
        .card {
            background-color: var(--secondary-bg);
            border: 1px solid var(--accent-bg);
        }
        
        .btn-primary {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }
        
        .btn-primary:hover {
            background-color: #0097e6;
            border-color: #0097e6;
        }
        
        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }
        
        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }
        
        .form-input {
            background-color: var(--accent-bg);
            border: 1px solid #555;
            color: var(--primary-text);
        }
        
        .form-input:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px rgba(0, 168, 255, 0.2);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="min-h-screen">
    <div class="flex h-screen">
        <!-- Sidebar -->
        {% if user.is_authenticated %}
        <div class="sidebar w-64 flex-shrink-0">
            <div class="p-4">
                <h2 class="text-xl font-bold text-center mb-6">متجر أدوات طب الأسنان</h2>
                
                <nav class="space-y-2">
                    {% if user.is_admin %}
                    <a href="{% url 'dashboard:home' %}" class="nav-item flex items-center px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-tachometer-alt ml-3"></i>
                        لوحة التحكم
                    </a>
                    {% endif %}
                    
                    <a href="{% url 'pos:home' %}" class="nav-item flex items-center px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-cash-register ml-3"></i>
                        نقطة البيع
                    </a>
                    
                    {% if user.is_admin %}
                    <a href="{% url 'products:list' %}" class="nav-item flex items-center px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-boxes ml-3"></i>
                        المنتجات
                    </a>
                    
                    <a href="{% url 'packages:list' %}" class="nav-item flex items-center px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-box-open ml-3"></i>
                        الحزم الدراسية
                    </a>
                    
                    <a href="{% url 'reports:home' %}" class="nav-item flex items-center px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-chart-bar ml-3"></i>
                        التقارير
                    </a>
                    {% endif %}
                    
                    <a href="{% url 'pos:customer_list' %}" class="nav-item flex items-center px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-users ml-3"></i>
                        العملاء
                    </a>
                </nav>
            </div>
            
            <!-- User Menu -->
            <div class="absolute bottom-0 w-64 p-4 border-t border-gray-600">
                <div class="flex items-center mb-2">
                    <i class="fas fa-user-circle text-2xl ml-3"></i>
                    <div>
                        <div class="font-medium">{{ user.get_full_name|default:user.username }}</div>
                        <div class="text-sm text-gray-400">{{ user.get_user_type_display }}</div>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <a href="{% url 'accounts:profile' %}" class="text-sm text-blue-400 hover:text-blue-300">الملف الشخصي</a>
                    <a href="{% url 'accounts:logout' %}" class="text-sm text-red-400 hover:text-red-300">تسجيل الخروج</a>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            {% if user.is_authenticated %}
            <header class="bg-gray-800 border-b border-gray-600 px-6 py-4">
                <div class="flex items-center justify-between">
                    <h1 class="text-xl font-semibold">{% block page_title %}الصفحة الرئيسية{% endblock %}</h1>
                    
                    <div class="flex items-center space-x-4">
                        <!-- Notifications -->
                        <a href="{% url 'dashboard:notifications' %}" class="relative">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                        </a>
                        
                        <!-- Current Time -->
                        <div class="text-sm text-gray-400" id="current-time"></div>
                    </div>
                </div>
            </header>
            {% endif %}
            
            <!-- Messages -->
            {% if messages %}
            <div class="px-6 py-2">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} mb-2 p-3 rounded-lg {% if message.tags == 'success' %}bg-green-600{% elif message.tags == 'error' %}bg-red-600{% elif message.tags == 'warning' %}bg-yellow-600{% else %}bg-blue-600{% endif %}">
                    {{ message }}
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-EG', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        setInterval(updateTime, 1000);
        updateTime();
        
        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
