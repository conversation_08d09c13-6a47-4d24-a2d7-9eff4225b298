<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}متجر أدوات طب الأسنان{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-bg: #0a0a0a;
            --secondary-bg: #1a1a1a;
            --accent-bg: #2a2a2a;
            --navbar-bg: #111111;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --text-muted: #666666;
            --accent-color: #3b82f6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --border-color: #333333;
            --neon-blue: #60a5fa;
            --neon-green: #34d399;
            --neon-purple: #a78bfa;
            --neon-orange: #fb923c;
            --neon-pink: #f472b6;
            --neon-cyan: #22d3ee;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, #1a1a2e 100%);
            color: var(--text-primary);
            direction: rtl;
            text-align: right;
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Navbar Styles */
        .navbar {
            background: linear-gradient(135deg, var(--navbar-bg) 0%, var(--secondary-bg) 100%);
            border-bottom: 2px solid var(--border-color);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .navbar-brand {
            color: var(--neon-blue) !important;
            text-shadow: 0 0 15px rgba(96, 165, 250, 0.6);
            font-weight: bold;
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            text-shadow: 0 0 25px rgba(96, 165, 250, 0.8);
            transform: scale(1.05);
        }

        .nav-link {
            color: var(--text-secondary) !important;
            transition: all 0.3s ease;
            position: relative;
            padding: 0.5rem 1rem !important;
            border-radius: 0.5rem;
            margin: 0 0.25rem;
        }

        .nav-link:hover {
            color: var(--neon-blue) !important;
            background-color: rgba(96, 165, 250, 0.1);
            text-shadow: 0 0 8px rgba(96, 165, 250, 0.4);
            transform: translateY(-2px);
        }

        .nav-link.active {
            color: var(--neon-green) !important;
            background-color: rgba(52, 211, 153, 0.15);
            text-shadow: 0 0 10px rgba(52, 211, 153, 0.5);
            border: 1px solid rgba(52, 211, 153, 0.3);
        }

        .nav-icon {
            filter: drop-shadow(0 0 5px currentColor);
            margin-left: 0.5rem;
            transition: all 0.3s ease;
        }

        .nav-link:hover .nav-icon {
            filter: drop-shadow(0 0 10px currentColor);
            transform: scale(1.1);
        }

        /* Dropdown Styles */
        .dropdown-menu {
            background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--accent-bg) 100%);
            border: 1px solid var(--border-color);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(10px);
            border-radius: 0.75rem;
        }

        .dropdown-item {
            color: var(--text-secondary);
            transition: all 0.3s ease;
            border-radius: 0.5rem;
            margin: 0.25rem;
        }

        .dropdown-item:hover {
            background-color: rgba(96, 165, 250, 0.1);
            color: var(--neon-blue);
            text-shadow: 0 0 5px rgba(96, 165, 250, 0.3);
            transform: translateX(-3px);
        }

        /* Card Styles */
        .card {
            background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--accent-bg) 100%);
            border: 1px solid var(--border-color);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
            transform: translateY(-5px);
            border-color: rgba(96, 165, 250, 0.3);
        }

        /* Button Styles */
        .btn {
            border: none;
            border-radius: 0.75rem;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--accent-color) 0%, #2563eb 100%);
            color: white;
        }

        .btn-primary:hover {
            box-shadow: 0 0 25px rgba(59, 130, 246, 0.5);
            transform: translateY(-2px);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: white;
        }

        .btn-success:hover {
            box-shadow: 0 0 25px rgba(16, 185, 129, 0.5);
            transform: translateY(-2px);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
            color: white;
        }

        .btn-warning:hover {
            box-shadow: 0 0 25px rgba(245, 158, 11, 0.5);
            transform: translateY(-2px);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
            color: white;
        }

        .btn-danger:hover {
            box-shadow: 0 0 25px rgba(239, 68, 68, 0.5);
            transform: translateY(-2px);
        }

        /* Form Styles */
        .form-control, .form-select {
            background-color: var(--accent-bg);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            background-color: var(--secondary-bg);
            border-color: var(--neon-blue);
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
            color: var(--text-primary);
        }

        /* Neon Text Colors */
        .text-neon-blue {
            color: var(--neon-blue);
            text-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
        }

        .text-neon-green {
            color: var(--neon-green);
            text-shadow: 0 0 10px rgba(52, 211, 153, 0.5);
        }

        .text-neon-purple {
            color: var(--neon-purple);
            text-shadow: 0 0 10px rgba(167, 139, 250, 0.5);
        }

        .text-neon-orange {
            color: var(--neon-orange);
            text-shadow: 0 0 10px rgba(251, 146, 60, 0.5);
        }

        .text-neon-pink {
            color: var(--neon-pink);
            text-shadow: 0 0 10px rgba(244, 114, 182, 0.5);
        }

        .text-neon-cyan {
            color: var(--neon-cyan);
            text-shadow: 0 0 10px rgba(34, 211, 238, 0.5);
        }

        /* Icon Styles */
        .icon-neon {
            filter: drop-shadow(0 0 5px currentColor);
            transition: all 0.3s ease;
        }

        .icon-neon:hover {
            filter: drop-shadow(0 0 15px currentColor);
            transform: scale(1.1);
        }

        /* Table Styles */
        .table-dark {
            background-color: var(--secondary-bg);
            color: var(--text-primary);
        }

        .table-dark th {
            border-color: var(--border-color);
            background-color: var(--accent-bg);
        }

        .table-dark td {
            border-color: var(--border-color);
        }

        .table-dark tbody tr:hover {
            background-color: rgba(96, 165, 250, 0.1);
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--primary-bg);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, var(--accent-bg) 0%, var(--border-color) 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, var(--border-color) 0%, var(--neon-blue) 100%);
        }

        /* Notification Badge */
        .notification-badge {
            background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
            animation: pulse 2s infinite;
            box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.05);
            }
        }

        /* Loading Animation */
        .loading-spinner {
            border: 3px solid var(--accent-bg);
            border-top: 3px solid var(--neon-blue);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if user.is_authenticated %}
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg sticky-top">
        <div class="container-fluid">
            <!-- Brand -->
            <a class="navbar-brand d-flex align-items-center" href="{% url 'dashboard:home' %}">
                <i class="fas fa-tooth nav-icon text-neon-blue"></i>
                متجر أدوات طب الأسنان
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if user.is_admin %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'dashboard:home' %}">
                            <i class="fas fa-tachometer-alt nav-icon text-neon-blue icon-neon"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    {% endif %}

                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'pos:home' %}">
                            <i class="fas fa-cash-register nav-icon text-neon-green icon-neon"></i>
                            نقطة البيع
                        </a>
                    </li>

                    {% if user.is_admin %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'products:list' %}">
                            <i class="fas fa-boxes nav-icon text-neon-purple icon-neon"></i>
                            المنتجات
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'packages:list' %}">
                            <i class="fas fa-box-open nav-icon text-neon-orange icon-neon"></i>
                            الحزم الدراسية
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'reports:home' %}">
                            <i class="fas fa-chart-bar nav-icon text-neon-pink icon-neon"></i>
                            التقارير
                        </a>
                    </li>
                    {% endif %}

                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'pos:customer_list' %}">
                            <i class="fas fa-users nav-icon text-neon-cyan icon-neon"></i>
                            العملاء
                        </a>
                    </li>
                </ul>

                <!-- User Menu -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle nav-icon text-neon-blue icon-neon"></i>
                            {{ user.get_full_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-user-cog me-2 text-neon-blue"></i>
                                    الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-cog me-2 text-neon-green"></i>
                                    الإعدادات
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{% url 'accounts:logout' %}">
                                    <i class="fas fa-sign-out-alt me-2 text-neon-orange"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h1 class="h3 mb-1 text-neon-blue">{% block page_title %}متجر أدوات طب الأسنان{% endblock %}</h1>
                                <p class="text-muted mb-0">{% block page_description %}نظام إدارة شامل لمتجر أدوات طب الأسنان{% endblock %}</p>
                            </div>

                            <!-- Quick Actions -->
                            <div class="d-flex gap-2">
                                <a href="{% url 'pos:new_invoice' %}" class="btn btn-success">
                                    <i class="fas fa-plus me-2 icon-neon"></i>
                                    فاتورة جديدة
                                </a>

                                <!-- Notifications -->
                                <button class="btn btn-outline-primary position-relative">
                                    <i class="fas fa-bell icon-neon"></i>
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge">
                                        3
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages -->
        {% if messages %}
        <div class="row mb-4">
            <div class="col-12">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Page Content -->
        <div class="row">
            <div class="col-12">
                {% block content %}
                <div class="card text-center">
                    <div class="card-body py-5">
                        <i class="fas fa-tooth text-neon-blue mb-4" style="font-size: 4rem;"></i>
                        <h2 class="h3 mb-4 text-neon-blue">مرحباً بك في متجر أدوات طب الأسنان</h2>
                        <p class="text-muted mb-4">نظام إدارة شامل لمتجر أدوات طب الأسنان مع واجهة حديثة وميزات متقدمة</p>

                        <div class="row g-3 mt-4">
                            <div class="col-md-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-cash-register text-neon-green mb-3 icon-neon" style="font-size: 2rem;"></i>
                                        <h5 class="text-neon-green">نقطة البيع</h5>
                                        <p class="text-muted small">إدارة المبيعات والفواتير</p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-boxes text-neon-purple mb-3 icon-neon" style="font-size: 2rem;"></i>
                                        <h5 class="text-neon-purple">المنتجات</h5>
                                        <p class="text-muted small">إدارة المخزون والمنتجات</p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-users text-neon-cyan mb-3 icon-neon" style="font-size: 2rem;"></i>
                                        <h5 class="text-neon-cyan">العملاء</h5>
                                        <p class="text-muted small">إدارة بيانات العملاء</p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-bar text-neon-pink mb-3 icon-neon" style="font-size: 2rem;"></i>
                                        <h5 class="text-neon-pink">التقارير</h5>
                                        <p class="text-muted small">تقارير المبيعات والأرباح</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endblock %}
            </div>
        </div>
    </div>

    {% else %}
    <!-- Login Page Layout -->
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        {% block content %}
        <div class="card" style="max-width: 400px; width: 100%;">
            <div class="card-body text-center py-5">
                <i class="fas fa-tooth text-neon-blue mb-4 icon-neon" style="font-size: 4rem;"></i>
                <h2 class="h3 mb-4 text-neon-blue">متجر أدوات طب الأسنان</h2>
                <p class="text-muted mb-4">يرجى تسجيل الدخول للمتابعة</p>
                <a href="{% url 'accounts:login' %}" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </a>
            </div>
        </div>
        {% endblock %}
    </div>
    {% endif %}

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    {% block extra_js %}{% endblock %}

    <script>
        // Active navigation highlighting
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Auto-refresh notifications (every 30 seconds)
        setInterval(function() {
            // Update notification count here
            console.log('Checking for new notifications...');
        }, 30000);

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
