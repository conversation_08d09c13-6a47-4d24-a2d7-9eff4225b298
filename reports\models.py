from django.db import models
from django.contrib.auth import get_user_model
from products.models import Product

User = get_user_model()

class Purchase(models.Model):
    """
    نموذج المشتريات من الموردين
    """
    supplier_name = models.CharField(
        max_length=200,
        verbose_name='اسم المورد'
    )
    supplier_phone = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        verbose_name='هاتف المورد'
    )
    purchase_date = models.DateField(
        verbose_name='تاريخ الشراء'
    )
    total_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name='المبلغ الإجمالي'
    )
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='أنشئ بواسطة'
    )

    class Meta:
        verbose_name = 'مشترى'
        verbose_name_plural = 'المشتريات'
        ordering = ['-purchase_date']

    def __str__(self):
        return f"مشترى من {self.supplier_name} - {self.purchase_date}"

class PurchaseItem(models.Model):
    """
    عناصر المشتريات
    """
    purchase = models.ForeignKey(
        Purchase,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name='المشترى'
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        verbose_name='المنتج'
    )
    quantity = models.PositiveIntegerField(
        verbose_name='الكمية'
    )
    unit_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='تكلفة الوحدة'
    )

    class Meta:
        verbose_name = 'عنصر مشترى'
        verbose_name_plural = 'عناصر المشتريات'

    def __str__(self):
        return f"{self.purchase} - {self.product.name}"

    @property
    def total_cost(self):
        """التكلفة الإجمالية للعنصر"""
        return self.quantity * self.unit_cost

class FinancialReport(models.Model):
    """
    التقارير المالية
    """
    REPORT_TYPES = (
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري'),
        ('yearly', 'سنوي'),
    )

    report_type = models.CharField(
        max_length=10,
        choices=REPORT_TYPES,
        verbose_name='نوع التقرير'
    )
    start_date = models.DateField(
        verbose_name='تاريخ البداية'
    )
    end_date = models.DateField(
        verbose_name='تاريخ النهاية'
    )
    total_sales = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        verbose_name='إجمالي المبيعات'
    )
    total_purchases = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        verbose_name='إجمالي المشتريات'
    )
    total_profit = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        verbose_name='إجمالي الربح'
    )
    invoice_count = models.PositiveIntegerField(
        default=0,
        verbose_name='عدد الفواتير'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='أنشئ بواسطة'
    )

    class Meta:
        verbose_name = 'تقرير مالي'
        verbose_name_plural = 'التقارير المالية'
        ordering = ['-created_at']

    def __str__(self):
        return f"تقرير {self.get_report_type_display()} - {self.start_date} إلى {self.end_date}"
