{% extends 'base/base.html' %}

{% block title %}تسجيل الدخول - متجر أدوات طب الأسنان{% endblock %}
{% block page_title %}تسجيل الدخول{% endblock %}
{% block page_description %}أدخل بياناتك للوصول إلى النظام{% endblock %}

{% block content %}
<div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
    <div class="row justify-content-center w-100">
        <div class="col-md-6 col-lg-4">
            <div class="card">
                <div class="card-body p-5">
                    <!-- Logo and Title -->
                    <div class="text-center mb-4">
                        <i class="fas fa-tooth text-neon-blue mb-3 icon-neon" style="font-size: 4rem;"></i>
                        <h2 class="h3 mb-2 text-neon-blue">متجر أدوات طب الأسنان</h2>
                        <p class="text-muted">نظام إدارة شامل ومتطور</p>
                    </div>
                    
                    <!-- Login Form -->
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <!-- Error Messages -->
                        {% if form.errors %}
                        <div class="alert alert-danger mb-4">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <!-- Username Field -->
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-2 text-neon-blue icon-neon"></i>
                                اسم المستخدم
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user text-neon-blue"></i>
                                </span>
                                {{ form.username }}
                            </div>
                            {% if form.username.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.username.errors.0 }}
                            </div>
                            {% endif %}
                        </div>
                        
                        <!-- Password Field -->
                        <div class="mb-4">
                            <label for="{{ form.password.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-2 text-neon-green icon-neon"></i>
                                كلمة المرور
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock text-neon-green"></i>
                                </span>
                                {{ form.password }}
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye" id="toggleIcon"></i>
                                </button>
                            </div>
                            {% if form.password.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.password.errors.0 }}
                            </div>
                            {% endif %}
                        </div>
                        
                        <!-- Remember Me -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="rememberMe" name="remember_me">
                                <label class="form-check-label" for="rememberMe">
                                    <i class="fas fa-check-circle me-2 text-neon-purple"></i>
                                    تذكرني
                                </label>
                            </div>
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                                <div class="loading-spinner d-none ms-2"></div>
                            </button>
                        </div>
                        
                        <!-- Additional Links -->
                        <div class="text-center">
                            <a href="#" class="text-neon-orange text-decoration-none">
                                <i class="fas fa-question-circle me-1"></i>
                                نسيت كلمة المرور؟
                            </a>
                        </div>
                    </form>
                    
                    <!-- System Info -->
                    <div class="text-center mt-4 pt-4 border-top">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt me-1 text-neon-green"></i>
                            نظام آمن ومحمي
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Features Cards -->
            <div class="row g-3 mt-4">
                <div class="col-4">
                    <div class="card text-center">
                        <div class="card-body py-3">
                            <i class="fas fa-cash-register text-neon-green mb-2" style="font-size: 1.5rem;"></i>
                            <small class="d-block text-muted">نقطة البيع</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-4">
                    <div class="card text-center">
                        <div class="card-body py-3">
                            <i class="fas fa-boxes text-neon-purple mb-2" style="font-size: 1.5rem;"></i>
                            <small class="d-block text-muted">إدارة المخزون</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-4">
                    <div class="card text-center">
                        <div class="card-body py-3">
                            <i class="fas fa-chart-bar text-neon-pink mb-2" style="font-size: 1.5rem;"></i>
                            <small class="d-block text-muted">التقارير</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .input-group-text {
        background-color: var(--accent-bg);
        border-color: var(--border-color);
        color: var(--text-secondary);
    }
    
    .form-control {
        border-right: none;
    }
    
    .form-control:focus {
        border-color: var(--neon-blue);
        box-shadow: none;
    }
    
    .form-control:focus + .input-group-text {
        border-color: var(--neon-blue);
    }
    
    .btn-outline-secondary {
        background-color: var(--accent-bg);
        border-color: var(--border-color);
        color: var(--text-secondary);
    }
    
    .btn-outline-secondary:hover {
        background-color: var(--border-color);
        border-color: var(--neon-blue);
        color: var(--neon-blue);
    }
    
    .form-check-input:checked {
        background-color: var(--neon-purple);
        border-color: var(--neon-purple);
    }
    
    .form-check-input:focus {
        border-color: var(--neon-purple);
        box-shadow: 0 0 0 0.25rem rgba(167, 139, 250, 0.25);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordField = document.getElementById('{{ form.password.id_for_label }}');
        const toggleIcon = document.getElementById('toggleIcon');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    });
    
    // Form submission with loading state
    document.querySelector('form').addEventListener('submit', function() {
        const submitBtn = document.querySelector('button[type="submit"]');
        const spinner = submitBtn.querySelector('.loading-spinner');
        
        submitBtn.disabled = true;
        spinner.classList.remove('d-none');
        
        setTimeout(function() {
            submitBtn.disabled = false;
            spinner.classList.add('d-none');
        }, 3000);
    });
    
    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            const forms = document.getElementsByClassName('needs-validation');
            Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
    
    // Auto-focus on username field
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('{{ form.username.id_for_label }}').focus();
    });
</script>
{% endblock %}
