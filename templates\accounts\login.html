<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - متجر أدوات طب الأسنان</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-card {
            background: rgba(45, 45, 45, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .form-input {
            background: rgba(61, 61, 61, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .form-input:focus {
            border-color: #00a8ff;
            box-shadow: 0 0 0 2px rgba(0, 168, 255, 0.2);
            outline: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #00a8ff 0%, #0097e6 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 168, 255, 0.3);
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="login-card w-full max-w-md p-8 rounded-2xl shadow-2xl">
        <!-- Logo/Header -->
        <div class="text-center mb-8">
            <div class="bg-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-tooth text-2xl text-white"></i>
            </div>
            <h1 class="text-2xl font-bold text-white mb-2">متجر أدوات طب الأسنان</h1>
            <p class="text-gray-400">تسجيل الدخول إلى النظام</p>
        </div>
        
        <!-- Messages -->
        {% if messages %}
        <div class="mb-6">
            {% for message in messages %}
            <div class="p-3 rounded-lg mb-2 {% if message.tags == 'error' %}bg-red-600 text-white{% else %}bg-green-600 text-white{% endif %}">
                <i class="fas {% if message.tags == 'error' %}fa-exclamation-circle{% else %}fa-check-circle{% endif %} ml-2"></i>
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <!-- Login Form -->
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div>
                <label for="username" class="block text-sm font-medium text-gray-300 mb-2">
                    <i class="fas fa-user ml-2"></i>
                    اسم المستخدم
                </label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    required
                    class="form-input w-full px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500 transition-all"
                    placeholder="أدخل اسم المستخدم"
                    autocomplete="username"
                >
            </div>
            
            <div>
                <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
                    <i class="fas fa-lock ml-2"></i>
                    كلمة المرور
                </label>
                <div class="relative">
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        required
                        class="form-input w-full px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500 transition-all"
                        placeholder="أدخل كلمة المرور"
                        autocomplete="current-password"
                    >
                    <button 
                        type="button" 
                        onclick="togglePassword()"
                        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                    >
                        <i class="fas fa-eye" id="password-toggle"></i>
                    </button>
                </div>
            </div>
            
            <button 
                type="submit" 
                class="btn-primary w-full py-3 px-4 rounded-lg font-medium text-white transition-all"
            >
                <i class="fas fa-sign-in-alt ml-2"></i>
                تسجيل الدخول
            </button>
        </form>
        
        <!-- Footer -->
        <div class="mt-8 text-center">
            <p class="text-sm text-gray-400">
                © 2025 متجر أدوات طب الأسنان. جميع الحقوق محفوظة.
            </p>
        </div>
    </div>
    
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('password-toggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
        
        // Auto-hide messages
        setTimeout(function() {
            const messages = document.querySelectorAll('.mb-6 > div');
            messages.forEach(message => {
                message.style.transition = 'opacity 0.5s';
                message.style.opacity = '0';
                setTimeout(() => message.remove(), 500);
            });
        }, 5000);
        
        // Focus on username field
        document.getElementById('username').focus();
    </script>
</body>
</html>
