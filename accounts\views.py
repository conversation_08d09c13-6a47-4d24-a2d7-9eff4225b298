from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse
from django_otp.decorators import otp_required
from django_otp import user_has_device
from django_otp.plugins.otp_totp.models import TOTPDevice
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
import qrcode
import io
import base64

def login_view(request):
    """
    صفحة تسجيل الدخول
    """
    if request.user.is_authenticated:
        return redirect('dashboard:home')

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(request, username=username, password=password)
        if user is not None:
            if user.is_active:
                login(request, user)
                # التحقق من وجود OTP
                if user_has_device(user):
                    return redirect('accounts:otp_verify')
                else:
                    # إعادة توجيه حسب نوع المستخدم
                    if user.is_admin:
                        return redirect('dashboard:home')
                    else:
                        return redirect('pos:home')
            else:
                messages.error(request, 'حسابك غير نشط. يرجى التواصل مع المدير.')
        else:
            messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة.')

    return render(request, 'accounts/login.html')

@login_required
def logout_view(request):
    """
    تسجيل الخروج
    """
    logout(request)
    messages.success(request, 'تم تسجيل الخروج بنجاح.')
    return redirect('accounts:login')

@login_required
def otp_setup(request):
    """
    إعداد المصادقة الثنائية
    """
    user = request.user
    device = TOTPDevice.objects.filter(user=user, confirmed=True).first()

    if not device:
        device = TOTPDevice.objects.create(
            user=user,
            name=f'{user.username}-device',
            confirmed=False
        )

    # إنشاء QR Code
    qr_url = device.config_url
    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data(qr_url)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    buffer.seek(0)
    qr_code_data = base64.b64encode(buffer.getvalue()).decode()

    context = {
        'qr_code': qr_code_data,
        'secret_key': device.key,
        'device': device
    }

    return render(request, 'accounts/otp_setup.html', context)

@login_required
@require_http_methods(["POST"])
def otp_verify_setup(request):
    """
    التحقق من إعداد OTP
    """
    token = request.POST.get('token')
    user = request.user

    device = TOTPDevice.objects.filter(user=user, confirmed=False).first()
    if device and device.verify_token(token):
        device.confirmed = True
        device.save()
        messages.success(request, 'تم تفعيل المصادقة الثنائية بنجاح.')
        return JsonResponse({'success': True})
    else:
        return JsonResponse({'success': False, 'error': 'الرمز غير صحيح.'})

@login_required
def otp_verify(request):
    """
    التحقق من OTP عند تسجيل الدخول
    """
    if request.method == 'POST':
        token = request.POST.get('token')
        user = request.user

        device = TOTPDevice.objects.filter(user=user, confirmed=True).first()
        if device and device.verify_token(token):
            # تسجيل نجاح OTP
            request.session['otp_verified'] = True

            # إعادة توجيه حسب نوع المستخدم
            if user.is_admin:
                return redirect('dashboard:home')
            else:
                return redirect('pos:home')
        else:
            messages.error(request, 'الرمز غير صحيح.')

    return render(request, 'accounts/otp_verify.html')

@login_required
def profile(request):
    """
    صفحة الملف الشخصي
    """
    user = request.user
    has_otp = user_has_device(user)

    if request.method == 'POST':
        # تحديث المعلومات الشخصية
        user.first_name = request.POST.get('first_name', '')
        user.last_name = request.POST.get('last_name', '')
        user.email = request.POST.get('email', '')
        user.phone = request.POST.get('phone', '')
        user.save()

        messages.success(request, 'تم تحديث المعلومات بنجاح.')
        return redirect('accounts:profile')

    context = {
        'user': user,
        'has_otp': has_otp
    }

    return render(request, 'accounts/profile.html', context)
