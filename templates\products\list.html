{% extends 'base/base.html' %}

{% block title %}إدارة المنتجات - متجر أدوات طب الأسنان{% endblock %}
{% block page_title %}إدارة المنتجات{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="card rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold mb-2">إدارة المنتجات</h2>
                <p class="text-gray-400">عرض وإدارة جميع المنتجات والمخزون</p>
            </div>
            <div class="flex space-x-4">
                <a href="{% url 'products:add' %}" class="bg-green-600 hover:bg-green-700 px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-plus ml-2"></i>
                    منتج جديد
                </a>
                <a href="{% url 'products:category_list' %}" class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-tags ml-2"></i>
                    إدارة الفئات
                </a>
                <a href="{% url 'products:low_stock' %}" class="bg-orange-600 hover:bg-orange-700 px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    مخزون منخفض
                </a>
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">إجمالي المنتجات</p>
                    <p class="text-2xl font-bold text-blue-400">{{ products.count }}</p>
                </div>
                <div class="bg-blue-600 p-3 rounded-full">
                    <i class="fas fa-boxes text-xl text-white"></i>
                </div>
            </div>
        </div>
        
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">منتجات نشطة</p>
                    <p class="text-2xl font-bold text-green-400">0</p>
                </div>
                <div class="bg-green-600 p-3 rounded-full">
                    <i class="fas fa-check-circle text-xl text-white"></i>
                </div>
            </div>
        </div>
        
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">مخزون منخفض</p>
                    <p class="text-2xl font-bold text-orange-400">0</p>
                </div>
                <div class="bg-orange-600 p-3 rounded-full">
                    <i class="fas fa-exclamation-triangle text-xl text-white"></i>
                </div>
            </div>
        </div>
        
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">قيمة المخزون</p>
                    <p class="text-2xl font-bold text-purple-400">0.00 جنيه</p>
                </div>
                <div class="bg-purple-600 p-3 rounded-full">
                    <i class="fas fa-dollar-sign text-xl text-white"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="card rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium mb-2">البحث</label>
                <div class="relative">
                    <input type="text" id="search-input" class="form-input w-full px-4 py-2 pl-10 rounded-lg" placeholder="ابحث عن منتج...">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium mb-2">الفئة</label>
                <select id="category-filter" class="form-input w-full px-4 py-2 rounded-lg">
                    <option value="">جميع الفئات</option>
                    <!-- سيتم تحميل الفئات ديناميكياً -->
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium mb-2">الحالة</label>
                <select id="status-filter" class="form-input w-full px-4 py-2 rounded-lg">
                    <option value="">جميع المنتجات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                    <option value="low_stock">مخزون منخفض</option>
                    <option value="out_of_stock">نفد المخزون</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium mb-2">ترتيب حسب</label>
                <select id="sort-select" class="form-input w-full px-4 py-2 rounded-lg">
                    <option value="name">الاسم</option>
                    <option value="price">السعر</option>
                    <option value="stock">المخزون</option>
                    <option value="created_at">تاريخ الإضافة</option>
                </select>
            </div>
        </div>
    </div>
    
    <!-- Products Table -->
    <div class="card rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">قائمة المنتجات</h3>
            <div class="flex items-center space-x-2">
                <button type="button" onclick="toggleView('grid')" id="grid-view-btn" class="p-2 rounded bg-gray-600 hover:bg-gray-700">
                    <i class="fas fa-th"></i>
                </button>
                <button type="button" onclick="toggleView('list')" id="list-view-btn" class="p-2 rounded bg-blue-600">
                    <i class="fas fa-list"></i>
                </button>
            </div>
        </div>
        
        <!-- List View -->
        <div id="list-view" class="overflow-x-auto">
            <table class="w-full text-sm">
                <thead>
                    <tr class="border-b border-gray-600">
                        <th class="text-right py-3 px-4">
                            <input type="checkbox" id="select-all" class="rounded">
                        </th>
                        <th class="text-right py-3 px-4">المنتج</th>
                        <th class="text-right py-3 px-4">الفئة</th>
                        <th class="text-right py-3 px-4">السعر</th>
                        <th class="text-right py-3 px-4">المخزون</th>
                        <th class="text-right py-3 px-4">الحد الأدنى</th>
                        <th class="text-right py-3 px-4">الحالة</th>
                        <th class="text-right py-3 px-4">تاريخ الإضافة</th>
                        <th class="text-right py-3 px-4">إجراءات</th>
                    </tr>
                </thead>
                <tbody id="products-table-body">
                    {% if products %}
                        {% for product in products %}
                        <tr class="border-b border-gray-600 hover:bg-gray-700">
                            <td class="py-3 px-4">
                                <input type="checkbox" class="product-checkbox rounded" value="{{ product.id }}">
                            </td>
                            <td class="py-3 px-4">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-gray-600 rounded-lg flex items-center justify-center mr-3">
                                        {% if product.image %}
                                            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="w-full h-full object-cover rounded-lg">
                                        {% else %}
                                            <i class="fas fa-box text-gray-400"></i>
                                        {% endif %}
                                    </div>
                                    <div>
                                        <div class="font-medium">{{ product.name }}</div>
                                        <div class="text-xs text-gray-400">{{ product.sku }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="py-3 px-4">{{ product.category.name|default:"-" }}</td>
                            <td class="py-3 px-4">{{ product.price }} جنيه</td>
                            <td class="py-3 px-4">
                                <span class="{% if product.quantity_in_stock <= product.minimum_stock_level %}text-red-400{% else %}text-green-400{% endif %}">
                                    {{ product.quantity_in_stock }}
                                </span>
                            </td>
                            <td class="py-3 px-4">{{ product.minimum_stock_level }}</td>
                            <td class="py-3 px-4">
                                <span class="px-2 py-1 rounded-full text-xs {% if product.is_active %}bg-green-600{% else %}bg-gray-600{% endif %}">
                                    {% if product.is_active %}نشط{% else %}غير نشط{% endif %}
                                </span>
                            </td>
                            <td class="py-3 px-4">{{ product.created_at|date:"Y/m/d" }}</td>
                            <td class="py-3 px-4">
                                <div class="flex items-center space-x-2">
                                    <a href="{% url 'products:detail' product.id %}" class="text-blue-400 hover:text-blue-300" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'products:edit' product.id %}" class="text-green-400 hover:text-green-300" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" onclick="deleteProduct({{ product.id }})" class="text-red-400 hover:text-red-300" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="9" class="text-center py-8 text-gray-400">
                                <i class="fas fa-boxes text-4xl mb-4"></i>
                                <p>لا توجد منتجات حتى الآن</p>
                                <a href="{% url 'products:add' %}" class="text-blue-400 hover:text-blue-300 mt-2 inline-block">
                                    إضافة أول منتج
                                </a>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        <!-- Grid View -->
        <div id="grid-view" class="hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {% if products %}
                    {% for product in products %}
                    <div class="card rounded-lg p-4 hover:bg-gray-700 transition-colors">
                        <div class="aspect-square bg-gray-600 rounded-lg mb-4 flex items-center justify-center">
                            {% if product.image %}
                                <img src="{{ product.image.url }}" alt="{{ product.name }}" class="w-full h-full object-cover rounded-lg">
                            {% else %}
                                <i class="fas fa-box text-4xl text-gray-400"></i>
                            {% endif %}
                        </div>
                        
                        <div class="space-y-2">
                            <h4 class="font-medium truncate">{{ product.name }}</h4>
                            <p class="text-sm text-gray-400">{{ product.category.name|default:"بدون فئة" }}</p>
                            <p class="text-lg font-bold text-green-400">{{ product.price }} جنيه</p>
                            
                            <div class="flex items-center justify-between text-sm">
                                <span class="{% if product.quantity_in_stock <= product.minimum_stock_level %}text-red-400{% else %}text-green-400{% endif %}">
                                    المخزون: {{ product.quantity_in_stock }}
                                </span>
                                <span class="px-2 py-1 rounded-full text-xs {% if product.is_active %}bg-green-600{% else %}bg-gray-600{% endif %}">
                                    {% if product.is_active %}نشط{% else %}غير نشط{% endif %}
                                </span>
                            </div>
                            
                            <div class="flex items-center space-x-2 pt-2">
                                <a href="{% url 'products:detail' product.id %}" class="flex-1 bg-blue-600 hover:bg-blue-700 py-2 px-3 rounded text-center text-sm">
                                    عرض
                                </a>
                                <a href="{% url 'products:edit' product.id %}" class="flex-1 bg-green-600 hover:bg-green-700 py-2 px-3 rounded text-center text-sm">
                                    تعديل
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="col-span-full text-center py-8 text-gray-400">
                        <i class="fas fa-boxes text-4xl mb-4"></i>
                        <p>لا توجد منتجات حتى الآن</p>
                        <a href="{% url 'products:add' %}" class="text-blue-400 hover:text-blue-300 mt-2 inline-block">
                            إضافة أول منتج
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Pagination -->
        <div class="flex items-center justify-between mt-6">
            <div class="text-sm text-gray-400">
                عرض {{ products.count }} منتج
            </div>
            
            <div class="flex items-center space-x-2">
                <button class="px-3 py-1 rounded bg-gray-600 hover:bg-gray-700 disabled:opacity-50" disabled>
                    <i class="fas fa-chevron-right"></i>
                </button>
                <span class="px-3 py-1 bg-blue-600 rounded">1</span>
                <button class="px-3 py-1 rounded bg-gray-600 hover:bg-gray-700 disabled:opacity-50" disabled>
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // View toggle functionality
    function toggleView(viewType) {
        const listView = document.getElementById('list-view');
        const gridView = document.getElementById('grid-view');
        const listBtn = document.getElementById('list-view-btn');
        const gridBtn = document.getElementById('grid-view-btn');
        
        if (viewType === 'grid') {
            listView.classList.add('hidden');
            gridView.classList.remove('hidden');
            listBtn.classList.remove('bg-blue-600');
            listBtn.classList.add('bg-gray-600');
            gridBtn.classList.remove('bg-gray-600');
            gridBtn.classList.add('bg-blue-600');
        } else {
            gridView.classList.add('hidden');
            listView.classList.remove('hidden');
            gridBtn.classList.remove('bg-blue-600');
            gridBtn.classList.add('bg-gray-600');
            listBtn.classList.remove('bg-gray-600');
            listBtn.classList.add('bg-blue-600');
        }
        
        // Save preference
        localStorage.setItem('products-view', viewType);
    }
    
    // Search functionality
    document.getElementById('search-input').addEventListener('input', function() {
        // Implement search functionality
        console.log('Searching for:', this.value);
    });
    
    // Filter functionality
    document.getElementById('category-filter').addEventListener('change', function() {
        // Implement category filtering
        console.log('Filter by category:', this.value);
    });
    
    document.getElementById('status-filter').addEventListener('change', function() {
        // Implement status filtering
        console.log('Filter by status:', this.value);
    });
    
    // Sort functionality
    document.getElementById('sort-select').addEventListener('change', function() {
        // Implement sorting
        console.log('Sort by:', this.value);
    });
    
    // Select all functionality
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.product-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // Delete product
    function deleteProduct(productId) {
        if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            // Implement delete functionality
            alert(`سيتم حذف المنتج ${productId}`);
        }
    }
    
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        // Restore view preference
        const savedView = localStorage.getItem('products-view') || 'list';
        toggleView(savedView);
    });
</script>
{% endblock %}
