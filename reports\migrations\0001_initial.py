# Generated by Django 5.2.4 on 2025-07-31 11:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FinancialReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_type', models.CharField(choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري'), ('yearly', 'سنوي')], max_length=10, verbose_name='نوع التقرير')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('total_sales', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي المبيعات')),
                ('total_purchases', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي المشتريات')),
                ('total_profit', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي الربح')),
                ('invoice_count', models.PositiveIntegerField(default=0, verbose_name='عدد الفواتير')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'تقرير مالي',
                'verbose_name_plural': 'التقارير المالية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Purchase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('supplier_name', models.CharField(max_length=200, verbose_name='اسم المورد')),
                ('supplier_phone', models.CharField(blank=True, max_length=15, null=True, verbose_name='هاتف المورد')),
                ('purchase_date', models.DateField(verbose_name='تاريخ الشراء')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ الإجمالي')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'مشترى',
                'verbose_name_plural': 'المشتريات',
                'ordering': ['-purchase_date'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='تكلفة الوحدة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='المنتج')),
                ('purchase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='reports.purchase', verbose_name='المشترى')),
            ],
            options={
                'verbose_name': 'عنصر مشترى',
                'verbose_name_plural': 'عناصر المشتريات',
            },
        ),
    ]
