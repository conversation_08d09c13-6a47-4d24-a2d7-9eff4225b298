# Generated by Django 5.2.4 on 2025-07-31 11:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('packages', '0001_initial'),
        ('products', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم العميل')),
                ('phone', models.CharField(max_length=15, unique=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('total_purchases', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي المشتريات')),
                ('purchase_count', models.PositiveIntegerField(default=0, verbose_name='عدد المشتريات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عميل',
                'verbose_name_plural': 'العملاء',
                'ordering': ['-total_purchases'],
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=20, unique=True, verbose_name='رقم الفاتورة')),
                ('subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المجموع الفرعي')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الخصم')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الضريبة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المبلغ الإجمالي')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('card', 'بطاقة'), ('transfer', 'تحويل')], default='cash', max_length=10, verbose_name='طريقة الدفع')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=10, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to='pos.customer', verbose_name='العميل')),
            ],
            options={
                'verbose_name': 'فاتورة',
                'verbose_name_plural': 'الفواتير',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_type', models.CharField(choices=[('product', 'منتج'), ('package', 'حزمة')], default='product', max_length=10, verbose_name='نوع العنصر')),
                ('quantity', models.PositiveIntegerField(default=1, verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الخصم')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='pos.invoice', verbose_name='الفاتورة')),
                ('package', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='packages.package', verbose_name='الحزمة')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر فاتورة',
                'verbose_name_plural': 'عناصر الفواتير',
            },
        ),
    ]
