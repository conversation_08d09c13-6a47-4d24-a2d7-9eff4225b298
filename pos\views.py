from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from .models import Customer, Invoice, InvoiceItem

@login_required
def home(request):
    """الصفحة الرئيسية لنقطة البيع"""
    return render(request, 'pos/home.html')

@login_required
def new_invoice(request):
    """إنشاء فاتورة جديدة"""
    return render(request, 'pos/new_invoice.html')

@login_required
def invoice_detail(request, invoice_id):
    """تفاصيل الفاتورة"""
    invoice = get_object_or_404(Invoice, id=invoice_id)
    return render(request, 'pos/invoice_detail.html', {'invoice': invoice})

@login_required
def print_invoice(request, invoice_id):
    """طباعة الفاتورة"""
    invoice = get_object_or_404(Invoice, id=invoice_id)
    return render(request, 'pos/print_invoice.html', {'invoice': invoice})

@login_required
def customer_list(request):
    """قائمة العملاء"""
    customers = Customer.objects.all().order_by('-created_at')
    return render(request, 'pos/customers.html', {'customers': customers})

@login_required
def customer_add(request):
    """إضافة عميل جديد"""
    if request.method == 'POST':
        # سيتم تطوير هذا لاحقاً
        messages.success(request, 'تم إضافة العميل بنجاح.')
        return redirect('pos:customer_list')

    return render(request, 'pos/customer_add.html')

@login_required
def product_search_api(request):
    """البحث عن المنتجات - API"""
    query = request.GET.get('q', '')
    # سيتم تطوير هذا لاحقاً
    return JsonResponse({'products': []})

@login_required
def package_search_api(request):
    """البحث عن الحزم - API"""
    query = request.GET.get('q', '')
    # سيتم تطوير هذا لاحقاً
    return JsonResponse({'packages': []})
