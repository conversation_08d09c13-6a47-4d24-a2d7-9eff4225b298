from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import HttpResponse
from .models import Purchase, PurchaseItem, FinancialReport

@login_required
@staff_member_required
def reports_home(request):
    """الصفحة الرئيسية للتقارير"""
    return render(request, 'reports/home.html')

@login_required
@staff_member_required
def financial_reports(request):
    """التقارير المالية"""
    reports = FinancialReport.objects.all().order_by('-created_at')
    return render(request, 'reports/financial.html', {'reports': reports})

@login_required
@staff_member_required
def sales_reports(request):
    """تقارير المبيعات"""
    return render(request, 'reports/sales.html')

@login_required
@staff_member_required
def inventory_reports(request):
    """تقارير المخزون"""
    return render(request, 'reports/inventory.html')

@login_required
@staff_member_required
def purchase_list(request):
    """قائمة المشتريات"""
    purchases = Purchase.objects.all().order_by('-purchase_date')
    return render(request, 'reports/purchases.html', {'purchases': purchases})

@login_required
@staff_member_required
def purchase_add(request):
    """إضافة مشترى جديد"""
    if request.method == 'POST':
        # سيتم تطوير هذا لاحقاً
        messages.success(request, 'تم إضافة المشترى بنجاح.')
        return redirect('reports:purchase_list')

    return render(request, 'reports/purchase_add.html')

@login_required
@staff_member_required
def export_pdf(request):
    """تصدير التقارير PDF"""
    # سيتم تطوير هذا لاحقاً
    return HttpResponse("PDF Export - قريباً")
