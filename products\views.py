from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import JsonResponse
from django.db import models
from .models import Product, Category

@login_required
@staff_member_required
def product_list(request):
    """قائمة المنتجات"""
    products = Product.objects.all().order_by('-created_at')
    return render(request, 'products/list.html', {'products': products})

@login_required
@staff_member_required
def product_add(request):
    """إضافة منتج جديد"""
    if request.method == 'POST':
        # سيتم تطوير هذا لاحقاً
        messages.success(request, 'تم إضافة المنتج بنجاح.')
        return redirect('products:list')

    categories = Category.objects.all()
    return render(request, 'products/add.html', {'categories': categories})

@login_required
def product_detail(request, product_id):
    """تفاصيل المنتج"""
    product = get_object_or_404(Product, id=product_id)
    return render(request, 'products/detail.html', {'product': product})

@login_required
@staff_member_required
def product_edit(request, product_id):
    """تعديل المنتج"""
    product = get_object_or_404(Product, id=product_id)
    if request.method == 'POST':
        # سيتم تطوير هذا لاحقاً
        messages.success(request, 'تم تحديث المنتج بنجاح.')
        return redirect('products:detail', product_id=product.id)

    categories = Category.objects.all()
    return render(request, 'products/edit.html', {'product': product, 'categories': categories})

@login_required
@staff_member_required
def product_delete(request, product_id):
    """حذف المنتج"""
    product = get_object_or_404(Product, id=product_id)
    if request.method == 'POST':
        product.delete()
        messages.success(request, 'تم حذف المنتج بنجاح.')
        return redirect('products:list')

    return render(request, 'products/delete.html', {'product': product})

@login_required
@staff_member_required
def category_list(request):
    """قائمة الفئات"""
    categories = Category.objects.all()
    return render(request, 'products/categories.html', {'categories': categories})

@login_required
@staff_member_required
def category_add(request):
    """إضافة فئة جديدة"""
    if request.method == 'POST':
        # سيتم تطوير هذا لاحقاً
        messages.success(request, 'تم إضافة الفئة بنجاح.')
        return redirect('products:category_list')

    return render(request, 'products/category_add.html')

@login_required
def low_stock_products(request):
    """المنتجات منخفضة المخزون"""
    products = Product.objects.filter(
        quantity_in_stock__lte=models.F('minimum_stock_level'),
        is_active=True
    )
    return render(request, 'products/low_stock.html', {'products': products})
