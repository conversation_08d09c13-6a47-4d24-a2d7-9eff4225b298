from django.contrib.auth.models import AbstractUser
from django.db import models

class User(AbstractUser):
    """
    نموذج المستخدم المخصص للنظام
    يدعم نوعين من المستخدمين: Admin و Sales
    """
    USER_TYPES = (
        ('admin', 'مدير'),
        ('sales', 'مبيعات'),
    )

    user_type = models.CharField(
        max_length=10,
        choices=USER_TYPES,
        default='sales',
        verbose_name='نوع المستخدم'
    )
    phone = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        verbose_name='رقم الهاتف'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )

    class Meta:
        verbose_name = 'مستخدم'
        verbose_name_plural = 'المستخدمون'

    def __str__(self):
        return f"{self.username} ({self.get_user_type_display()})"

    @property
    def is_admin(self):
        return self.user_type == 'admin'

    @property
    def is_sales(self):
        return self.user_type == 'sales'
