{% extends 'base/base.html' %}

{% block title %}إضافة عميل جديد - متجر أدوات طب الأسنان{% endblock %}
{% block page_title %}إضافة عميل جديد{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="card rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold mb-2">إضافة عميل جديد</h2>
                <p class="text-gray-400">أدخل بيانات العميل الجديد</p>
            </div>
            <a href="{% url 'pos:customer_list' %}" class="bg-gray-600 hover:bg-gray-700 px-6 py-3 rounded-lg font-medium transition-colors">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
    
    <!-- Customer Form -->
    <form id="customer-form" class="space-y-6">
        {% csrf_token %}
        
        <!-- Basic Information -->
        <div class="card rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">المعلومات الأساسية</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium mb-2">اسم العميل <span class="text-red-400">*</span></label>
                    <input type="text" id="name" name="name" required class="form-input w-full px-4 py-3 rounded-lg" placeholder="أدخل اسم العميل الكامل">
                    <div class="text-red-400 text-sm mt-1 hidden" id="name-error"></div>
                </div>
                
                <div>
                    <label for="phone" class="block text-sm font-medium mb-2">رقم الهاتف <span class="text-red-400">*</span></label>
                    <input type="tel" id="phone" name="phone" required class="form-input w-full px-4 py-3 rounded-lg" placeholder="01xxxxxxxxx">
                    <div class="text-red-400 text-sm mt-1 hidden" id="phone-error"></div>
                </div>
                
                <div>
                    <label for="email" class="block text-sm font-medium mb-2">البريد الإلكتروني</label>
                    <input type="email" id="email" name="email" class="form-input w-full px-4 py-3 rounded-lg" placeholder="<EMAIL>">
                    <div class="text-red-400 text-sm mt-1 hidden" id="email-error"></div>
                </div>
                
                <div>
                    <label for="birth_date" class="block text-sm font-medium mb-2">تاريخ الميلاد</label>
                    <input type="date" id="birth_date" name="birth_date" class="form-input w-full px-4 py-3 rounded-lg">
                </div>
                
                <div>
                    <label for="gender" class="block text-sm font-medium mb-2">الجنس</label>
                    <select id="gender" name="gender" class="form-input w-full px-4 py-3 rounded-lg">
                        <option value="">اختر الجنس</option>
                        <option value="male">ذكر</option>
                        <option value="female">أنثى</option>
                    </select>
                </div>
                
                <div>
                    <label for="customer_type" class="block text-sm font-medium mb-2">نوع العميل</label>
                    <select id="customer_type" name="customer_type" class="form-input w-full px-4 py-3 rounded-lg">
                        <option value="regular">عميل عادي</option>
                        <option value="student">طالب</option>
                        <option value="doctor">طبيب</option>
                        <option value="clinic">عيادة</option>
                        <option value="hospital">مستشفى</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Address Information -->
        <div class="card rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">معلومات العنوان</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label for="address" class="block text-sm font-medium mb-2">العنوان</label>
                    <textarea id="address" name="address" rows="3" class="form-input w-full px-4 py-3 rounded-lg" placeholder="أدخل العنوان التفصيلي"></textarea>
                </div>
                
                <div>
                    <label for="city" class="block text-sm font-medium mb-2">المدينة</label>
                    <input type="text" id="city" name="city" class="form-input w-full px-4 py-3 rounded-lg" placeholder="أدخل اسم المدينة">
                </div>
                
                <div>
                    <label for="governorate" class="block text-sm font-medium mb-2">المحافظة</label>
                    <select id="governorate" name="governorate" class="form-input w-full px-4 py-3 rounded-lg">
                        <option value="">اختر المحافظة</option>
                        <option value="cairo">القاهرة</option>
                        <option value="giza">الجيزة</option>
                        <option value="alexandria">الإسكندرية</option>
                        <option value="qalyubia">القليوبية</option>
                        <option value="port_said">بورسعيد</option>
                        <option value="suez">السويس</option>
                        <option value="luxor">الأقصر</option>
                        <option value="aswan">أسوان</option>
                        <option value="asyut">أسيوط</option>
                        <option value="beheira">البحيرة</option>
                        <option value="beni_suef">بني سويف</option>
                        <option value="dakahlia">الدقهلية</option>
                        <option value="damietta">دمياط</option>
                        <option value="fayyum">الفيوم</option>
                        <option value="gharbia">الغربية</option>
                        <option value="ismailia">الإسماعيلية</option>
                        <option value="kafr_el_sheikh">كفر الشيخ</option>
                        <option value="matrouh">مطروح</option>
                        <option value="minya">المنيا</option>
                        <option value="monufia">المنوفية</option>
                        <option value="new_valley">الوادي الجديد</option>
                        <option value="north_sinai">شمال سيناء</option>
                        <option value="qena">قنا</option>
                        <option value="red_sea">البحر الأحمر</option>
                        <option value="sharqia">الشرقية</option>
                        <option value="sohag">سوهاج</option>
                        <option value="south_sinai">جنوب سيناء</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Additional Information -->
        <div class="card rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">معلومات إضافية</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="university" class="block text-sm font-medium mb-2">الجامعة (للطلاب)</label>
                    <input type="text" id="university" name="university" class="form-input w-full px-4 py-3 rounded-lg" placeholder="اسم الجامعة">
                </div>
                
                <div>
                    <label for="academic_year" class="block text-sm font-medium mb-2">السنة الدراسية (للطلاب)</label>
                    <select id="academic_year" name="academic_year" class="form-input w-full px-4 py-3 rounded-lg">
                        <option value="">اختر السنة الدراسية</option>
                        <option value="1">السنة الأولى</option>
                        <option value="2">السنة الثانية</option>
                        <option value="3">السنة الثالثة</option>
                        <option value="4">السنة الرابعة</option>
                        <option value="5">السنة الخامسة</option>
                    </select>
                </div>
                
                <div>
                    <label for="discount_percentage" class="block text-sm font-medium mb-2">نسبة الخصم (%)</label>
                    <input type="number" id="discount_percentage" name="discount_percentage" min="0" max="100" step="0.01" class="form-input w-full px-4 py-3 rounded-lg" placeholder="0.00">
                </div>
                
                <div>
                    <label for="credit_limit" class="block text-sm font-medium mb-2">حد الائتمان (جنيه)</label>
                    <input type="number" id="credit_limit" name="credit_limit" min="0" step="0.01" class="form-input w-full px-4 py-3 rounded-lg" placeholder="0.00">
                </div>
                
                <div class="md:col-span-2">
                    <label for="notes" class="block text-sm font-medium mb-2">ملاحظات</label>
                    <textarea id="notes" name="notes" rows="3" class="form-input w-full px-4 py-3 rounded-lg" placeholder="أي ملاحظات إضافية عن العميل"></textarea>
                </div>
            </div>
        </div>
        
        <!-- Settings -->
        <div class="card rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">الإعدادات</h3>
            
            <div class="space-y-4">
                <div class="flex items-center">
                    <input type="checkbox" id="is_active" name="is_active" checked class="rounded mr-3">
                    <label for="is_active" class="text-sm font-medium">عميل نشط</label>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" id="receive_notifications" name="receive_notifications" checked class="rounded mr-3">
                    <label for="receive_notifications" class="text-sm font-medium">استقبال الإشعارات</label>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" id="receive_offers" name="receive_offers" checked class="rounded mr-3">
                    <label for="receive_offers" class="text-sm font-medium">استقبال العروض والخصومات</label>
                </div>
            </div>
        </div>
        
        <!-- Form Actions -->
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-400">
                    <span class="text-red-400">*</span> الحقول المطلوبة
                </div>
                
                <div class="flex space-x-4">
                    <button type="button" onclick="resetForm()" class="bg-gray-600 hover:bg-gray-700 px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-undo ml-2"></i>
                        إعادة تعيين
                    </button>
                    
                    <button type="submit" class="bg-green-600 hover:bg-green-700 px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-save ml-2"></i>
                        حفظ العميل
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Form validation and submission
    document.getElementById('customer-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (validateForm()) {
            saveCustomer();
        }
    });
    
    // Phone number formatting
    document.getElementById('phone').addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length > 11) {
            value = value.slice(0, 11);
        }
        this.value = value;
    });
    
    // Customer type change handler
    document.getElementById('customer_type').addEventListener('change', function() {
        const universityField = document.getElementById('university');
        const academicYearField = document.getElementById('academic_year');
        
        if (this.value === 'student') {
            universityField.parentElement.style.display = 'block';
            academicYearField.parentElement.style.display = 'block';
        } else {
            universityField.parentElement.style.display = 'none';
            academicYearField.parentElement.style.display = 'none';
            universityField.value = '';
            academicYearField.value = '';
        }
    });
    
    function validateForm() {
        let isValid = true;
        
        // Clear previous errors
        document.querySelectorAll('.text-red-400').forEach(el => {
            if (el.id.endsWith('-error')) {
                el.classList.add('hidden');
                el.textContent = '';
            }
        });
        
        // Validate name
        const name = document.getElementById('name').value.trim();
        if (!name) {
            showError('name-error', 'اسم العميل مطلوب');
            isValid = false;
        } else if (name.length < 2) {
            showError('name-error', 'اسم العميل يجب أن يكون أكثر من حرفين');
            isValid = false;
        }
        
        // Validate phone
        const phone = document.getElementById('phone').value.trim();
        if (!phone) {
            showError('phone-error', 'رقم الهاتف مطلوب');
            isValid = false;
        } else if (!/^01[0-9]{9}$/.test(phone)) {
            showError('phone-error', 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ 01 ويتكون من 11 رقم)');
            isValid = false;
        }
        
        // Validate email if provided
        const email = document.getElementById('email').value.trim();
        if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            showError('email-error', 'البريد الإلكتروني غير صحيح');
            isValid = false;
        }
        
        return isValid;
    }
    
    function showError(elementId, message) {
        const errorElement = document.getElementById(elementId);
        errorElement.textContent = message;
        errorElement.classList.remove('hidden');
    }
    
    function saveCustomer() {
        const formData = new FormData(document.getElementById('customer-form'));
        
        // Show loading state
        const submitButton = document.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الحفظ...';
        submitButton.disabled = true;
        
        // Simulate API call - replace with actual implementation
        setTimeout(() => {
            // Reset button state
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
            
            // Show success message
            alert('تم حفظ العميل بنجاح!');
            
            // Redirect to customer list
            window.location.href = "{% url 'pos:customer_list' %}";
        }, 2000);
    }
    
    function resetForm() {
        if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع البيانات المدخلة.')) {
            document.getElementById('customer-form').reset();
            
            // Clear errors
            document.querySelectorAll('.text-red-400').forEach(el => {
                if (el.id.endsWith('-error')) {
                    el.classList.add('hidden');
                    el.textContent = '';
                }
            });
            
            // Reset customer type dependent fields
            document.getElementById('customer_type').dispatchEvent(new Event('change'));
        }
    }
    
    // Initialize form
    document.addEventListener('DOMContentLoaded', function() {
        // Trigger customer type change to hide/show relevant fields
        document.getElementById('customer_type').dispatchEvent(new Event('change'));
        
        // Set default date to today
        document.getElementById('birth_date').max = new Date().toISOString().split('T')[0];
    });
</script>
{% endblock %}
