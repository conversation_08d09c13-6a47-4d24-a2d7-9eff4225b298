# Generated by Django 5.2.4 on 2025-07-31 11:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('notification_type', models.CharField(choices=[('low_stock', 'مخزون منخفض'), ('new_order', 'طلب جديد'), ('system', 'نظام'), ('warning', 'تحذير')], default='system', max_length=20, verbose_name='نوع الإشعار')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي'), ('urgent', 'عاجل')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'إشعار',
                'verbose_name_plural': 'الإشعارات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('store_name', models.CharField(default='متجر أدوات طب الأسنان', max_length=200, verbose_name='اسم المتجر')),
                ('store_address', models.TextField(blank=True, null=True, verbose_name='عنوان المتجر')),
                ('store_phone', models.CharField(blank=True, max_length=15, null=True, verbose_name='هاتف المتجر')),
                ('store_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='بريد المتجر')),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='معدل الضريبة')),
                ('currency', models.CharField(default='EGP', max_length=10, verbose_name='العملة')),
                ('low_stock_threshold', models.PositiveIntegerField(default=3, verbose_name='حد المخزون المنخفض')),
                ('invoice_footer', models.TextField(blank=True, null=True, verbose_name='تذييل الفاتورة')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='حُدث بواسطة')),
            ],
            options={
                'verbose_name': 'إعدادات النظام',
                'verbose_name_plural': 'إعدادات النظام',
            },
        ),
    ]
