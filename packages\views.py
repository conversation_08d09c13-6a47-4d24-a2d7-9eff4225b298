from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from .models import Package, AcademicYear, PackageItem

@login_required
@staff_member_required
def package_list(request):
    """قائمة الحزم"""
    packages = Package.objects.all().order_by('academic_year__year')
    return render(request, 'packages/list.html', {'packages': packages})

@login_required
@staff_member_required
def package_add(request):
    """إضافة حزمة جديدة"""
    if request.method == 'POST':
        # سيتم تطوير هذا لاحقاً
        messages.success(request, 'تم إضافة الحزمة بنجاح.')
        return redirect('packages:list')

    academic_years = AcademicYear.objects.all()
    return render(request, 'packages/add.html', {'academic_years': academic_years})

@login_required
def package_detail(request, package_id):
    """تفاصيل الحزمة"""
    package = get_object_or_404(Package, id=package_id)
    return render(request, 'packages/detail.html', {'package': package})

@login_required
@staff_member_required
def package_edit(request, package_id):
    """تعديل الحزمة"""
    package = get_object_or_404(Package, id=package_id)
    if request.method == 'POST':
        # سيتم تطوير هذا لاحقاً
        messages.success(request, 'تم تحديث الحزمة بنجاح.')
        return redirect('packages:detail', package_id=package.id)

    academic_years = AcademicYear.objects.all()
    return render(request, 'packages/edit.html', {'package': package, 'academic_years': academic_years})

@login_required
@staff_member_required
def package_delete(request, package_id):
    """حذف الحزمة"""
    package = get_object_or_404(Package, id=package_id)
    if request.method == 'POST':
        package.delete()
        messages.success(request, 'تم حذف الحزمة بنجاح.')
        return redirect('packages:list')

    return render(request, 'packages/delete.html', {'package': package})

@login_required
@staff_member_required
def academic_year_list(request):
    """قائمة السنوات الدراسية"""
    academic_years = AcademicYear.objects.all()
    return render(request, 'packages/academic_years.html', {'academic_years': academic_years})
