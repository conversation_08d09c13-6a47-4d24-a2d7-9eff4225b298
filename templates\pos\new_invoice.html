{% extends 'base/base.html' %}

{% block title %}فاتورة جديدة - متجر أدوات طب الأسنان{% endblock %}
{% block page_title %}إنشاء فاتورة جديدة{% endblock %}

{% block extra_css %}
<style>
    .invoice-item {
        background-color: var(--accent-bg);
        border: 1px solid #555;
    }
    
    .product-search {
        position: relative;
    }
    
    .search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--secondary-bg);
        border: 1px solid #555;
        border-radius: 0.5rem;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }
    
    .search-result-item {
        padding: 0.75rem;
        cursor: pointer;
        border-bottom: 1px solid #555;
    }
    
    .search-result-item:hover {
        background-color: var(--accent-bg);
    }
    
    .invoice-total {
        background: linear-gradient(135deg, #00a8ff 0%, #0097e6 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Invoice Header -->
    <div class="card rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Customer Selection -->
            <div>
                <label class="block text-sm font-medium mb-2">العميل</label>
                <div class="flex space-x-2">
                    <select id="customer-select" class="form-input flex-1 px-4 py-2 rounded-lg">
                        <option value="">اختر العميل...</option>
                        <!-- سيتم تحميل العملاء ديناميكياً -->
                    </select>
                    <button type="button" onclick="showNewCustomerModal()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            
            <!-- Invoice Info -->
            <div>
                <label class="block text-sm font-medium mb-2">معلومات الفاتورة</label>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <input type="text" id="invoice-number" class="form-input w-full px-4 py-2 rounded-lg" placeholder="رقم الفاتورة" readonly>
                    </div>
                    <div>
                        <input type="date" id="invoice-date" class="form-input w-full px-4 py-2 rounded-lg" value="{{ today }}">
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Product Search -->
    <div class="card rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4">إضافة منتجات</h3>
        <div class="product-search">
            <input 
                type="text" 
                id="product-search" 
                class="form-input w-full px-4 py-3 rounded-lg" 
                placeholder="ابحث عن منتج أو حزمة..."
                autocomplete="off"
            >
            <div id="search-results" class="search-results"></div>
        </div>
    </div>
    
    <!-- Invoice Items -->
    <div class="card rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4">عناصر الفاتورة</h3>
        
        <div class="overflow-x-auto">
            <table class="w-full text-sm">
                <thead>
                    <tr class="border-b border-gray-600">
                        <th class="text-right py-3 px-4">المنتج</th>
                        <th class="text-right py-3 px-4">السعر</th>
                        <th class="text-right py-3 px-4">الكمية</th>
                        <th class="text-right py-3 px-4">الخصم</th>
                        <th class="text-right py-3 px-4">الإجمالي</th>
                        <th class="text-right py-3 px-4">إجراءات</th>
                    </tr>
                </thead>
                <tbody id="invoice-items">
                    <tr id="no-items-row">
                        <td colspan="6" class="text-center py-8 text-gray-400">
                            <i class="fas fa-shopping-cart text-4xl mb-4"></i>
                            <p>لا توجد عناصر في الفاتورة</p>
                            <p class="text-sm">ابحث عن منتج لإضافته</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Invoice Summary -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Payment & Notes -->
        <div class="card rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">تفاصيل إضافية</h3>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-2">طريقة الدفع</label>
                    <select id="payment-method" class="form-input w-full px-4 py-2 rounded-lg">
                        <option value="cash">نقدي</option>
                        <option value="card">بطاقة</option>
                        <option value="transfer">تحويل</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">ملاحظات</label>
                    <textarea id="invoice-notes" class="form-input w-full px-4 py-3 rounded-lg" rows="3" placeholder="ملاحظات إضافية..."></textarea>
                </div>
            </div>
        </div>
        
        <!-- Invoice Total -->
        <div class="card rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">إجمالي الفاتورة</h3>
            
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span>المجموع الفرعي:</span>
                    <span id="subtotal">0.00 جنيه</span>
                </div>
                
                <div class="flex justify-between">
                    <span>الخصم:</span>
                    <div class="flex items-center space-x-2">
                        <input type="number" id="discount-amount" class="form-input w-20 px-2 py-1 rounded text-center" value="0" min="0" step="0.01">
                        <span>جنيه</span>
                    </div>
                </div>
                
                <div class="flex justify-between">
                    <span>الضريبة:</span>
                    <div class="flex items-center space-x-2">
                        <input type="number" id="tax-amount" class="form-input w-20 px-2 py-1 rounded text-center" value="0" min="0" step="0.01">
                        <span>جنيه</span>
                    </div>
                </div>
                
                <hr class="border-gray-600">
                
                <div class="flex justify-between text-xl font-bold invoice-total text-white p-3 rounded-lg">
                    <span>الإجمالي:</span>
                    <span id="total-amount">0.00 جنيه</span>
                </div>
            </div>
            
            <div class="mt-6 space-y-3">
                <button type="button" onclick="saveInvoice()" class="w-full bg-green-600 hover:bg-green-700 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-save ml-2"></i>
                    حفظ الفاتورة
                </button>
                
                <button type="button" onclick="saveAndPrint()" class="w-full bg-blue-600 hover:bg-blue-700 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-print ml-2"></i>
                    حفظ وطباعة
                </button>
                
                <a href="{% url 'pos:home' %}" class="block w-full bg-gray-600 hover:bg-gray-700 py-3 rounded-lg font-medium transition-colors text-center">
                    <i class="fas fa-times ml-2"></i>
                    إلغاء
                </a>
            </div>
        </div>
    </div>
</div>

<!-- New Customer Modal -->
<div id="new-customer-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-semibold mb-4">إضافة عميل جديد</h3>
        
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium mb-2">اسم العميل</label>
                <input type="text" id="new-customer-name" class="form-input w-full px-4 py-2 rounded-lg" placeholder="أدخل اسم العميل">
            </div>
            
            <div>
                <label class="block text-sm font-medium mb-2">رقم الهاتف</label>
                <input type="tel" id="new-customer-phone" class="form-input w-full px-4 py-2 rounded-lg" placeholder="أدخل رقم الهاتف">
            </div>
            
            <div>
                <label class="block text-sm font-medium mb-2">البريد الإلكتروني (اختياري)</label>
                <input type="email" id="new-customer-email" class="form-input w-full px-4 py-2 rounded-lg" placeholder="أدخل البريد الإلكتروني">
            </div>
        </div>
        
        <div class="flex space-x-3 mt-6">
            <button type="button" onclick="saveNewCustomer()" class="flex-1 bg-green-600 hover:bg-green-700 py-2 rounded-lg">حفظ</button>
            <button type="button" onclick="hideNewCustomerModal()" class="flex-1 bg-gray-600 hover:bg-gray-700 py-2 rounded-lg">إلغاء</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let invoiceItems = [];
    let itemCounter = 0;
    
    // Initialize invoice
    document.addEventListener('DOMContentLoaded', function() {
        generateInvoiceNumber();
        setupProductSearch();
        updateTotals();
    });
    
    function generateInvoiceNumber() {
        const today = new Date();
        const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        document.getElementById('invoice-number').value = `INV${dateStr}${randomNum}`;
    }
    
    function setupProductSearch() {
        const searchInput = document.getElementById('product-search');
        const resultsDiv = document.getElementById('search-results');
        
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length >= 2) {
                // Simulate product search - replace with actual API call
                showSearchResults([
                    {id: 1, name: 'أدوات فحص الأسنان', price: 150.00, type: 'product'},
                    {id: 2, name: 'حزمة السنة الأولى', price: 2500.00, type: 'package'}
                ]);
            } else {
                hideSearchResults();
            }
        });
        
        // Hide results when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !resultsDiv.contains(e.target)) {
                hideSearchResults();
            }
        });
    }
    
    function showSearchResults(results) {
        const resultsDiv = document.getElementById('search-results');
        resultsDiv.innerHTML = '';
        
        results.forEach(item => {
            const div = document.createElement('div');
            div.className = 'search-result-item';
            div.innerHTML = `
                <div class="flex justify-between items-center">
                    <div>
                        <div class="font-medium">${item.name}</div>
                        <div class="text-sm text-gray-400">${item.type === 'product' ? 'منتج' : 'حزمة'}</div>
                    </div>
                    <div class="text-green-400 font-bold">${item.price.toFixed(2)} جنيه</div>
                </div>
            `;
            div.addEventListener('click', () => addItemToInvoice(item));
            resultsDiv.appendChild(div);
        });
        
        resultsDiv.style.display = 'block';
    }
    
    function hideSearchResults() {
        document.getElementById('search-results').style.display = 'none';
    }
    
    function addItemToInvoice(item) {
        const existingItem = invoiceItems.find(i => i.id === item.id && i.type === item.type);
        
        if (existingItem) {
            existingItem.quantity += 1;
            updateItemRow(existingItem);
        } else {
            const newItem = {
                ...item,
                quantity: 1,
                discount: 0,
                rowId: ++itemCounter
            };
            invoiceItems.push(newItem);
            addItemRow(newItem);
        }
        
        document.getElementById('product-search').value = '';
        hideSearchResults();
        updateTotals();
    }
    
    function addItemRow(item) {
        const tbody = document.getElementById('invoice-items');
        const noItemsRow = document.getElementById('no-items-row');
        
        if (noItemsRow) {
            noItemsRow.remove();
        }
        
        const row = document.createElement('tr');
        row.id = `item-row-${item.rowId}`;
        row.className = 'border-b border-gray-600';
        row.innerHTML = `
            <td class="py-3 px-4">
                <div class="font-medium">${item.name}</div>
                <div class="text-sm text-gray-400">${item.type === 'product' ? 'منتج' : 'حزمة'}</div>
            </td>
            <td class="py-3 px-4">${item.price.toFixed(2)} جنيه</td>
            <td class="py-3 px-4">
                <input type="number" value="${item.quantity}" min="1" class="form-input w-20 px-2 py-1 rounded text-center" 
                       onchange="updateItemQuantity(${item.rowId}, this.value)">
            </td>
            <td class="py-3 px-4">
                <input type="number" value="${item.discount}" min="0" step="0.01" class="form-input w-20 px-2 py-1 rounded text-center" 
                       onchange="updateItemDiscount(${item.rowId}, this.value)">
            </td>
            <td class="py-3 px-4 font-bold text-green-400" id="item-total-${item.rowId}">
                ${((item.price * item.quantity) - item.discount).toFixed(2)} جنيه
            </td>
            <td class="py-3 px-4">
                <button type="button" onclick="removeItem(${item.rowId})" class="text-red-400 hover:text-red-300">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
    }
    
    function updateItemQuantity(rowId, quantity) {
        const item = invoiceItems.find(i => i.rowId === rowId);
        if (item) {
            item.quantity = parseInt(quantity) || 1;
            updateItemRow(item);
            updateTotals();
        }
    }
    
    function updateItemDiscount(rowId, discount) {
        const item = invoiceItems.find(i => i.rowId === rowId);
        if (item) {
            item.discount = parseFloat(discount) || 0;
            updateItemRow(item);
            updateTotals();
        }
    }
    
    function updateItemRow(item) {
        const totalCell = document.getElementById(`item-total-${item.rowId}`);
        if (totalCell) {
            const total = (item.price * item.quantity) - item.discount;
            totalCell.textContent = `${total.toFixed(2)} جنيه`;
        }
    }
    
    function removeItem(rowId) {
        const itemIndex = invoiceItems.findIndex(i => i.rowId === rowId);
        if (itemIndex > -1) {
            invoiceItems.splice(itemIndex, 1);
            document.getElementById(`item-row-${rowId}`).remove();
            
            if (invoiceItems.length === 0) {
                const tbody = document.getElementById('invoice-items');
                tbody.innerHTML = `
                    <tr id="no-items-row">
                        <td colspan="6" class="text-center py-8 text-gray-400">
                            <i class="fas fa-shopping-cart text-4xl mb-4"></i>
                            <p>لا توجد عناصر في الفاتورة</p>
                            <p class="text-sm">ابحث عن منتج لإضافته</p>
                        </td>
                    </tr>
                `;
            }
            
            updateTotals();
        }
    }
    
    function updateTotals() {
        const subtotal = invoiceItems.reduce((sum, item) => {
            return sum + ((item.price * item.quantity) - item.discount);
        }, 0);
        
        const discountAmount = parseFloat(document.getElementById('discount-amount').value) || 0;
        const taxAmount = parseFloat(document.getElementById('tax-amount').value) || 0;
        const total = subtotal - discountAmount + taxAmount;
        
        document.getElementById('subtotal').textContent = `${subtotal.toFixed(2)} جنيه`;
        document.getElementById('total-amount').textContent = `${total.toFixed(2)} جنيه`;
    }
    
    // Event listeners for discount and tax changes
    document.getElementById('discount-amount').addEventListener('input', updateTotals);
    document.getElementById('tax-amount').addEventListener('input', updateTotals);
    
    // Modal functions
    function showNewCustomerModal() {
        document.getElementById('new-customer-modal').classList.remove('hidden');
        document.getElementById('new-customer-modal').classList.add('flex');
    }
    
    function hideNewCustomerModal() {
        document.getElementById('new-customer-modal').classList.add('hidden');
        document.getElementById('new-customer-modal').classList.remove('flex');
        // Clear form
        document.getElementById('new-customer-name').value = '';
        document.getElementById('new-customer-phone').value = '';
        document.getElementById('new-customer-email').value = '';
    }
    
    function saveNewCustomer() {
        const name = document.getElementById('new-customer-name').value.trim();
        const phone = document.getElementById('new-customer-phone').value.trim();
        
        if (!name || !phone) {
            alert('يرجى إدخال اسم العميل ورقم الهاتف');
            return;
        }
        
        // Simulate saving customer - replace with actual API call
        alert('تم إضافة العميل بنجاح');
        hideNewCustomerModal();
    }
    
    function saveInvoice() {
        if (invoiceItems.length === 0) {
            alert('يرجى إضافة عناصر للفاتورة');
            return;
        }
        
        // Simulate saving invoice - replace with actual API call
        alert('تم حفظ الفاتورة بنجاح');
    }
    
    function saveAndPrint() {
        if (invoiceItems.length === 0) {
            alert('يرجى إضافة عناصر للفاتورة');
            return;
        }
        
        // Simulate saving and printing - replace with actual API call
        alert('تم حفظ الفاتورة وسيتم طباعتها');
    }
</script>
{% endblock %}
