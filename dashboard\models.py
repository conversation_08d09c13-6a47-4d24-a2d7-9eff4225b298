from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Notification(models.Model):
    """
    نموذج الإشعارات
    """
    NOTIFICATION_TYPES = (
        ('low_stock', 'مخزون منخفض'),
        ('new_order', 'طلب جديد'),
        ('system', 'نظام'),
        ('warning', 'تحذير'),
    )

    PRIORITY_LEVELS = (
        ('low', 'منخفض'),
        ('medium', 'متوسط'),
        ('high', 'عالي'),
        ('urgent', 'عاجل'),
    )

    title = models.CharField(
        max_length=200,
        verbose_name='العنوان'
    )
    message = models.TextField(
        verbose_name='الرسالة'
    )
    notification_type = models.CharField(
        max_length=20,
        choices=NOTIFICATION_TYPES,
        default='system',
        verbose_name='نوع الإشعار'
    )
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_LEVELS,
        default='medium',
        verbose_name='الأولوية'
    )
    is_read = models.BooleanField(
        default=False,
        verbose_name='مقروء'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notifications',
        null=True,
        blank=True,
        verbose_name='المستخدم'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    read_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='تاريخ القراءة'
    )

    class Meta:
        verbose_name = 'إشعار'
        verbose_name_plural = 'الإشعارات'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.get_notification_type_display()}"

    def mark_as_read(self):
        """تحديد الإشعار كمقروء"""
        from django.utils import timezone
        self.is_read = True
        self.read_at = timezone.now()
        self.save()

class SystemSettings(models.Model):
    """
    إعدادات النظام
    """
    store_name = models.CharField(
        max_length=200,
        default='متجر أدوات طب الأسنان',
        verbose_name='اسم المتجر'
    )
    store_address = models.TextField(
        blank=True,
        null=True,
        verbose_name='عنوان المتجر'
    )
    store_phone = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        verbose_name='هاتف المتجر'
    )
    store_email = models.EmailField(
        blank=True,
        null=True,
        verbose_name='بريد المتجر'
    )
    tax_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name='معدل الضريبة'
    )
    currency = models.CharField(
        max_length=10,
        default='EGP',
        verbose_name='العملة'
    )
    low_stock_threshold = models.PositiveIntegerField(
        default=3,
        verbose_name='حد المخزون المنخفض'
    )
    invoice_footer = models.TextField(
        blank=True,
        null=True,
        verbose_name='تذييل الفاتورة'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='حُدث بواسطة'
    )

    class Meta:
        verbose_name = 'إعدادات النظام'
        verbose_name_plural = 'إعدادات النظام'

    def __str__(self):
        return self.store_name

    @classmethod
    def get_settings(cls):
        """الحصول على إعدادات النظام"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings
