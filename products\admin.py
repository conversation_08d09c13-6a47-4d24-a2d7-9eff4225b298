from django.contrib import admin
from .models import Category, Product

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_at')
    search_fields = ('name',)
    ordering = ('name',)

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'sku', 'category', 'selling_price', 'quantity_in_stock', 'is_low_stock', 'is_active')
    list_filter = ('category', 'is_active', 'created_at')
    search_fields = ('name', 'sku', 'description')
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'category', 'description', 'sku', 'image')
        }),
        ('الأسعار', {
            'fields': ('purchase_price', 'selling_price')
        }),
        ('المخزون', {
            'fields': ('quantity_in_stock', 'minimum_stock_level')
        }),
        ('إعدادات', {
            'fields': ('is_active', 'created_by')
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان منتج جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
