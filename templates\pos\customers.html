{% extends 'base/base.html' %}

{% block title %}إدارة العملاء - متجر أدوات طب الأسنان{% endblock %}
{% block page_title %}إدارة العملاء{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="card rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold mb-2">إدارة العملاء</h2>
                <p class="text-gray-400">عرض وإدارة بيانات العملاء</p>
            </div>
            <div class="flex space-x-4">
                <a href="{% url 'pos:customer_add' %}" class="bg-green-600 hover:bg-green-700 px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-plus ml-2"></i>
                    عميل جديد
                </a>
                <button type="button" onclick="exportCustomers()" class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-download ml-2"></i>
                    تصدير
                </button>
            </div>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="card rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium mb-2">البحث</label>
                <div class="relative">
                    <input type="text" id="search-input" class="form-input w-full px-4 py-2 pl-10 rounded-lg" placeholder="ابحث بالاسم أو الهاتف...">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium mb-2">ترتيب حسب</label>
                <select id="sort-select" class="form-input w-full px-4 py-2 rounded-lg">
                    <option value="name">الاسم</option>
                    <option value="created_at">تاريخ الإضافة</option>
                    <option value="total_purchases">إجمالي المشتريات</option>
                    <option value="last_purchase">آخر شراء</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium mb-2">فلترة</label>
                <select id="filter-select" class="form-input w-full px-4 py-2 rounded-lg">
                    <option value="all">جميع العملاء</option>
                    <option value="active">عملاء نشطون</option>
                    <option value="new">عملاء جدد</option>
                    <option value="vip">عملاء مميزون</option>
                </select>
            </div>
        </div>
    </div>
    
    <!-- Customer Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">إجمالي العملاء</p>
                    <p class="text-2xl font-bold text-blue-400">0</p>
                </div>
                <div class="bg-blue-600 p-3 rounded-full">
                    <i class="fas fa-users text-xl text-white"></i>
                </div>
            </div>
        </div>
        
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">عملاء جدد هذا الشهر</p>
                    <p class="text-2xl font-bold text-green-400">0</p>
                </div>
                <div class="bg-green-600 p-3 rounded-full">
                    <i class="fas fa-user-plus text-xl text-white"></i>
                </div>
            </div>
        </div>
        
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">عملاء نشطون</p>
                    <p class="text-2xl font-bold text-purple-400">0</p>
                </div>
                <div class="bg-purple-600 p-3 rounded-full">
                    <i class="fas fa-star text-xl text-white"></i>
                </div>
            </div>
        </div>
        
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">متوسط قيمة الشراء</p>
                    <p class="text-2xl font-bold text-orange-400">0.00 جنيه</p>
                </div>
                <div class="bg-orange-600 p-3 rounded-full">
                    <i class="fas fa-chart-line text-xl text-white"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Customers Table -->
    <div class="card rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">قائمة العملاء</h3>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-400">عرض</span>
                <select id="per-page-select" class="form-input px-3 py-1 rounded">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span class="text-sm text-gray-400">عميل</span>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full text-sm">
                <thead>
                    <tr class="border-b border-gray-600">
                        <th class="text-right py-3 px-4">
                            <input type="checkbox" id="select-all" class="rounded">
                        </th>
                        <th class="text-right py-3 px-4">اسم العميل</th>
                        <th class="text-right py-3 px-4">رقم الهاتف</th>
                        <th class="text-right py-3 px-4">البريد الإلكتروني</th>
                        <th class="text-right py-3 px-4">تاريخ التسجيل</th>
                        <th class="text-right py-3 px-4">عدد المشتريات</th>
                        <th class="text-right py-3 px-4">إجمالي المشتريات</th>
                        <th class="text-right py-3 px-4">آخر شراء</th>
                        <th class="text-right py-3 px-4">الحالة</th>
                        <th class="text-right py-3 px-4">إجراءات</th>
                    </tr>
                </thead>
                <tbody id="customers-table-body">
                    <tr>
                        <td colspan="10" class="text-center py-8 text-gray-400">
                            <i class="fas fa-users text-4xl mb-4"></i>
                            <p>لا يوجد عملاء مسجلون حتى الآن</p>
                            <a href="{% url 'pos:customer_add' %}" class="text-blue-400 hover:text-blue-300 mt-2 inline-block">
                                إضافة أول عميل
                            </a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="flex items-center justify-between mt-6">
            <div class="text-sm text-gray-400">
                عرض 0 من 0 عميل
            </div>
            
            <div class="flex items-center space-x-2">
                <button class="px-3 py-1 rounded bg-gray-600 hover:bg-gray-700 disabled:opacity-50" disabled>
                    <i class="fas fa-chevron-right"></i>
                </button>
                <span class="px-3 py-1 bg-blue-600 rounded">1</span>
                <button class="px-3 py-1 rounded bg-gray-600 hover:bg-gray-700 disabled:opacity-50" disabled>
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
        </div>
    </div>
    
    <!-- Bulk Actions -->
    <div id="bulk-actions" class="card rounded-lg p-4 hidden">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <span class="text-sm">تم تحديد <span id="selected-count">0</span> عميل</span>
                <button type="button" onclick="clearSelection()" class="text-blue-400 hover:text-blue-300 text-sm">
                    إلغاء التحديد
                </button>
            </div>
            
            <div class="flex items-center space-x-2">
                <button type="button" onclick="bulkExport()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-sm">
                    <i class="fas fa-download ml-1"></i>
                    تصدير المحدد
                </button>
                <button type="button" onclick="bulkDelete()" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg text-sm">
                    <i class="fas fa-trash ml-1"></i>
                    حذف المحدد
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let customers = [];
    let selectedCustomers = [];
    
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        loadCustomers();
        setupEventListeners();
    });
    
    function setupEventListeners() {
        // Search functionality
        document.getElementById('search-input').addEventListener('input', function() {
            filterCustomers();
        });
        
        // Sort functionality
        document.getElementById('sort-select').addEventListener('change', function() {
            sortCustomers();
        });
        
        // Filter functionality
        document.getElementById('filter-select').addEventListener('change', function() {
            filterCustomers();
        });
        
        // Select all checkbox
        document.getElementById('select-all').addEventListener('change', function() {
            toggleSelectAll();
        });
        
        // Per page selection
        document.getElementById('per-page-select').addEventListener('change', function() {
            updatePagination();
        });
    }
    
    function loadCustomers() {
        // Simulate loading customers - replace with actual API call
        customers = [
            // Sample data - will be replaced with real data
        ];
        
        renderCustomers();
        updateStats();
    }
    
    function renderCustomers() {
        const tbody = document.getElementById('customers-table-body');
        
        if (customers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center py-8 text-gray-400">
                        <i class="fas fa-users text-4xl mb-4"></i>
                        <p>لا يوجد عملاء مسجلون حتى الآن</p>
                        <a href="{% url 'pos:customer_add' %}" class="text-blue-400 hover:text-blue-300 mt-2 inline-block">
                            إضافة أول عميل
                        </a>
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = customers.map(customer => `
            <tr class="border-b border-gray-600 hover:bg-gray-700">
                <td class="py-3 px-4">
                    <input type="checkbox" class="customer-checkbox rounded" value="${customer.id}" onchange="toggleCustomerSelection(${customer.id})">
                </td>
                <td class="py-3 px-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                            ${customer.name.charAt(0)}
                        </div>
                        <div>
                            <div class="font-medium">${customer.name}</div>
                            <div class="text-xs text-gray-400">#${customer.id}</div>
                        </div>
                    </div>
                </td>
                <td class="py-3 px-4">${customer.phone}</td>
                <td class="py-3 px-4">${customer.email || '-'}</td>
                <td class="py-3 px-4">${formatDate(customer.created_at)}</td>
                <td class="py-3 px-4">${customer.purchase_count}</td>
                <td class="py-3 px-4">${customer.total_purchases.toFixed(2)} جنيه</td>
                <td class="py-3 px-4">${customer.last_purchase ? formatDate(customer.last_purchase) : '-'}</td>
                <td class="py-3 px-4">
                    <span class="px-2 py-1 rounded-full text-xs ${customer.is_active ? 'bg-green-600' : 'bg-gray-600'}">
                        ${customer.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td class="py-3 px-4">
                    <div class="flex items-center space-x-2">
                        <button type="button" onclick="viewCustomer(${customer.id})" class="text-blue-400 hover:text-blue-300" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" onclick="editCustomer(${customer.id})" class="text-green-400 hover:text-green-300" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" onclick="deleteCustomer(${customer.id})" class="text-red-400 hover:text-red-300" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    function updateStats() {
        // Update customer statistics
        const totalCustomers = customers.length;
        const newThisMonth = customers.filter(c => isThisMonth(c.created_at)).length;
        const activeCustomers = customers.filter(c => c.is_active).length;
        const avgPurchase = customers.length > 0 ? 
            customers.reduce((sum, c) => sum + c.total_purchases, 0) / customers.length : 0;
        
        // Update UI - implement actual updates here
    }
    
    function filterCustomers() {
        // Implement filtering logic
        renderCustomers();
    }
    
    function sortCustomers() {
        // Implement sorting logic
        renderCustomers();
    }
    
    function toggleSelectAll() {
        const selectAll = document.getElementById('select-all');
        const checkboxes = document.querySelectorAll('.customer-checkbox');
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
            const customerId = parseInt(checkbox.value);
            if (selectAll.checked) {
                if (!selectedCustomers.includes(customerId)) {
                    selectedCustomers.push(customerId);
                }
            } else {
                selectedCustomers = [];
            }
        });
        
        updateBulkActions();
    }
    
    function toggleCustomerSelection(customerId) {
        const index = selectedCustomers.indexOf(customerId);
        if (index > -1) {
            selectedCustomers.splice(index, 1);
        } else {
            selectedCustomers.push(customerId);
        }
        
        updateBulkActions();
    }
    
    function updateBulkActions() {
        const bulkActions = document.getElementById('bulk-actions');
        const selectedCount = document.getElementById('selected-count');
        
        if (selectedCustomers.length > 0) {
            bulkActions.classList.remove('hidden');
            selectedCount.textContent = selectedCustomers.length;
        } else {
            bulkActions.classList.add('hidden');
        }
    }
    
    function clearSelection() {
        selectedCustomers = [];
        document.querySelectorAll('.customer-checkbox').forEach(cb => cb.checked = false);
        document.getElementById('select-all').checked = false;
        updateBulkActions();
    }
    
    function viewCustomer(customerId) {
        // Implement view customer functionality
        alert(`عرض تفاصيل العميل ${customerId}`);
    }
    
    function editCustomer(customerId) {
        // Implement edit customer functionality
        alert(`تعديل العميل ${customerId}`);
    }
    
    function deleteCustomer(customerId) {
        if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
            // Implement delete functionality
            alert(`تم حذف العميل ${customerId}`);
        }
    }
    
    function exportCustomers() {
        // Implement export functionality
        alert('سيتم تصدير قائمة العملاء');
    }
    
    function bulkExport() {
        // Implement bulk export functionality
        alert(`سيتم تصدير ${selectedCustomers.length} عميل`);
    }
    
    function bulkDelete() {
        if (confirm(`هل أنت متأكد من حذف ${selectedCustomers.length} عميل؟`)) {
            // Implement bulk delete functionality
            alert(`تم حذف ${selectedCustomers.length} عميل`);
        }
    }
    
    // Utility functions
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-EG');
    }
    
    function isThisMonth(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
    }
</script>
{% endblock %}
