{% extends 'base/base.html' %}

{% block title %}الإشعارات - متجر أدوات طب الأسنان{% endblock %}
{% block page_title %}الإشعارات{% endblock %}
{% block page_description %}مركز الإشعارات والتنبيهات{% endblock %}

{% block content %}
<!-- Notifications Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1 text-neon-blue">
                            <i class="fas fa-bell me-2 icon-neon"></i>
                            مركز الإشعارات
                        </h5>
                        <p class="text-muted mb-0">جميع الإشعارات والتنبيهات الهامة</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="markAllAsRead()">
                            <i class="fas fa-check-double me-2"></i>
                            تحديد الكل كمقروء
                        </button>
                        <button class="btn btn-outline-danger" onclick="clearAllNotifications()">
                            <i class="fas fa-trash me-2"></i>
                            مسح الكل
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notification Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex gap-2 flex-wrap">
                    <button class="btn btn-primary active" data-filter="all">
                        <i class="fas fa-list me-2"></i>
                        الكل
                    </button>
                    <button class="btn btn-outline-success" data-filter="sales">
                        <i class="fas fa-shopping-cart me-2"></i>
                        المبيعات
                    </button>
                    <button class="btn btn-outline-warning" data-filter="inventory">
                        <i class="fas fa-boxes me-2"></i>
                        المخزون
                    </button>
                    <button class="btn btn-outline-info" data-filter="customers">
                        <i class="fas fa-users me-2"></i>
                        العملاء
                    </button>
                    <button class="btn btn-outline-danger" data-filter="system">
                        <i class="fas fa-cog me-2"></i>
                        النظام
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notifications List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <!-- Sample Notifications -->
                <div class="notification-item border-bottom pb-3 mb-3" data-type="sales">
                    <div class="d-flex align-items-start">
                        <div class="notification-icon bg-success rounded-circle p-2 me-3">
                            <i class="fas fa-shopping-cart text-white"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 text-neon-green">فاتورة جديدة</h6>
                            <p class="text-muted mb-1">تم إنشاء فاتورة جديدة برقم #001 بقيمة 1,500 جنيه</p>
                            <small class="text-muted">منذ 5 دقائق</small>
                        </div>
                        <div class="notification-actions">
                            <button class="btn btn-sm btn-outline-primary me-2">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="notification-item border-bottom pb-3 mb-3" data-type="inventory">
                    <div class="d-flex align-items-start">
                        <div class="notification-icon bg-warning rounded-circle p-2 me-3">
                            <i class="fas fa-exclamation-triangle text-white"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 text-neon-orange">تنبيه مخزون</h6>
                            <p class="text-muted mb-1">المنتج "أدوات الحشو" وصل إلى الحد الأدنى للمخزون</p>
                            <small class="text-muted">منذ 15 دقيقة</small>
                        </div>
                        <div class="notification-actions">
                            <button class="btn btn-sm btn-outline-primary me-2">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="notification-item border-bottom pb-3 mb-3" data-type="customers">
                    <div class="d-flex align-items-start">
                        <div class="notification-icon bg-info rounded-circle p-2 me-3">
                            <i class="fas fa-user-plus text-white"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 text-neon-cyan">عميل جديد</h6>
                            <p class="text-muted mb-1">تم تسجيل عميل جديد: د. أحمد محمد</p>
                            <small class="text-muted">منذ 30 دقيقة</small>
                        </div>
                        <div class="notification-actions">
                            <button class="btn btn-sm btn-outline-primary me-2">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="notification-item border-bottom pb-3 mb-3" data-type="system">
                    <div class="d-flex align-items-start">
                        <div class="notification-icon bg-primary rounded-circle p-2 me-3">
                            <i class="fas fa-sync text-white"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 text-neon-blue">تحديث النظام</h6>
                            <p class="text-muted mb-1">تم تحديث النظام بنجاح إلى الإصدار 2.1.0</p>
                            <small class="text-muted">منذ ساعة</small>
                        </div>
                        <div class="notification-actions">
                            <button class="btn btn-sm btn-outline-primary me-2">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Empty State -->
                <div class="text-center py-5 d-none" id="empty-notifications">
                    <i class="fas fa-bell-slash text-muted mb-3" style="font-size: 3rem;"></i>
                    <h5 class="text-muted">لا توجد إشعارات</h5>
                    <p class="text-muted">ستظهر الإشعارات الجديدة هنا</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notification Settings -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 text-neon-purple">
                    <i class="fas fa-cog me-2 icon-neon"></i>
                    إعدادات الإشعارات
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="salesNotifications" checked>
                            <label class="form-check-label" for="salesNotifications">
                                إشعارات المبيعات
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="inventoryNotifications" checked>
                            <label class="form-check-label" for="inventoryNotifications">
                                إشعارات المخزون
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="customerNotifications" checked>
                            <label class="form-check-label" for="customerNotifications">
                                إشعارات العملاء
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="systemNotifications" checked>
                            <label class="form-check-label" for="systemNotifications">
                                إشعارات النظام
                            </label>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        حفظ الإعدادات
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Filter notifications
    document.querySelectorAll('[data-filter]').forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.dataset.filter;
            
            // Update active button
            document.querySelectorAll('[data-filter]').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter notifications
            const notifications = document.querySelectorAll('.notification-item');
            notifications.forEach(notification => {
                if (filter === 'all' || notification.dataset.type === filter) {
                    notification.style.display = 'block';
                } else {
                    notification.style.display = 'none';
                }
            });
        });
    });
    
    // Mark all as read
    function markAllAsRead() {
        if (confirm('هل تريد تحديد جميع الإشعارات كمقروءة؟')) {
            document.querySelectorAll('.notification-item').forEach(item => {
                item.style.opacity = '0.6';
            });
            alert('تم تحديد جميع الإشعارات كمقروءة');
        }
    }
    
    // Clear all notifications
    function clearAllNotifications() {
        if (confirm('هل تريد مسح جميع الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.')) {
            document.querySelectorAll('.notification-item').forEach(item => {
                item.remove();
            });
            document.getElementById('empty-notifications').classList.remove('d-none');
        }
    }
    
    // Auto-refresh notifications every 30 seconds
    setInterval(function() {
        console.log('Checking for new notifications...');
        // Implement auto-refresh logic here
    }, 30000);
</script>
{% endblock %}
