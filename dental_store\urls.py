"""
URL configuration for dental_store project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.shortcuts import redirect

def home_redirect(request):
    """إعادة توجيه الصفحة الرئيسية"""
    if request.user.is_authenticated:
        if request.user.is_admin:
            return redirect('dashboard:home')
        else:
            return redirect('pos:home')
    return redirect('accounts:login')

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', home_redirect, name='home'),
    path('accounts/', include('accounts.urls')),
    path('products/', include('products.urls')),
    path('pos/', include('pos.urls')),
    path('packages/', include('packages.urls')),
    path('reports/', include('reports.urls')),
    path('dashboard/', include('dashboard.urls')),
]

# إعدادات الملفات الثابتة والوسائط
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
