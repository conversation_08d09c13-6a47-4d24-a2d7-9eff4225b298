{% extends 'base/base.html' %}

{% block title %}لوحة التحكم - متجر أدوات طب الأسنان{% endblock %}
{% block page_title %}لوحة التحكم{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Welcome Section -->
    <div class="card rounded-lg p-6">
        <h2 class="text-2xl font-bold mb-2">مرحباً، {{ user.get_full_name|default:user.username }}</h2>
        <p class="text-gray-400">إليك نظرة عامة على أداء المتجر اليوم</p>
    </div>
    
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Today's Sales -->
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">مبيعات اليوم</p>
                    <p class="text-2xl font-bold text-green-400">
                        {{ today_sales.total|default:0|floatformat:2 }} جنيه
                    </p>
                    <p class="text-sm text-gray-400">{{ today_sales.count|default:0 }} فاتورة</p>
                </div>
                <div class="bg-green-600 p-3 rounded-full">
                    <i class="fas fa-dollar-sign text-xl text-white"></i>
                </div>
            </div>
        </div>
        
        <!-- Monthly Sales -->
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">مبيعات الشهر</p>
                    <p class="text-2xl font-bold text-blue-400">
                        {{ month_sales.total|default:0|floatformat:2 }} جنيه
                    </p>
                    <p class="text-sm text-gray-400">{{ month_sales.count|default:0 }} فاتورة</p>
                </div>
                <div class="bg-blue-600 p-3 rounded-full">
                    <i class="fas fa-chart-line text-xl text-white"></i>
                </div>
            </div>
        </div>
        
        <!-- New Customers -->
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">عملاء جدد</p>
                    <p class="text-2xl font-bold text-purple-400">{{ new_customers }}</p>
                    <p class="text-sm text-gray-400">هذا الشهر</p>
                </div>
                <div class="bg-purple-600 p-3 rounded-full">
                    <i class="fas fa-users text-xl text-white"></i>
                </div>
            </div>
        </div>
        
        <!-- Low Stock Alert -->
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">مخزون منخفض</p>
                    <p class="text-2xl font-bold text-red-400">{{ low_stock_products|length }}</p>
                    <p class="text-sm text-gray-400">منتج</p>
                </div>
                <div class="bg-red-600 p-3 rounded-full">
                    <i class="fas fa-exclamation-triangle text-xl text-white"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts and Tables Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Top Products -->
        <div class="card rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">أفضل المنتجات مبيعاً</h3>
            <div class="space-y-3">
                {% for product in top_products %}
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div>
                        <p class="font-medium">{{ product.product__name }}</p>
                        <p class="text-sm text-gray-400">{{ product.total_quantity }} قطعة</p>
                    </div>
                    <div class="text-left">
                        <p class="font-bold text-green-400">{{ product.total_revenue|floatformat:2 }} جنيه</p>
                    </div>
                </div>
                {% empty %}
                <p class="text-gray-400 text-center py-4">لا توجد بيانات متاحة</p>
                {% endfor %}
            </div>
        </div>
        
        <!-- Low Stock Products -->
        <div class="card rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">منتجات بمخزون منخفض</h3>
                <a href="{% url 'products:low_stock' %}" class="text-blue-400 hover:text-blue-300 text-sm">
                    عرض الكل
                </a>
            </div>
            <div class="space-y-3">
                {% for product in low_stock_products %}
                <div class="flex items-center justify-between p-3 bg-red-900 bg-opacity-30 rounded-lg border border-red-600">
                    <div>
                        <p class="font-medium">{{ product.name }}</p>
                        <p class="text-sm text-gray-400">{{ product.sku }}</p>
                    </div>
                    <div class="text-left">
                        <p class="font-bold text-red-400">{{ product.quantity_in_stock }} قطعة</p>
                        <p class="text-xs text-gray-400">الحد الأدنى: {{ product.minimum_stock_level }}</p>
                    </div>
                </div>
                {% empty %}
                <p class="text-gray-400 text-center py-4">جميع المنتجات بمخزون كافي</p>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- Recent Notifications -->
    {% if unread_notifications %}
    <div class="card rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">الإشعارات الحديثة</h3>
            <a href="{% url 'dashboard:notifications' %}" class="text-blue-400 hover:text-blue-300 text-sm">
                عرض الكل
            </a>
        </div>
        <div class="space-y-3">
            {% for notification in unread_notifications %}
            <div class="flex items-start p-3 bg-gray-700 rounded-lg">
                <div class="flex-shrink-0 ml-3">
                    <i class="fas {% if notification.notification_type == 'low_stock' %}fa-exclamation-triangle text-red-400{% elif notification.notification_type == 'new_order' %}fa-shopping-cart text-green-400{% else %}fa-info-circle text-blue-400{% endif %}"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium">{{ notification.title }}</p>
                    <p class="text-sm text-gray-400">{{ notification.message|truncatewords:15 }}</p>
                    <p class="text-xs text-gray-500 mt-1">{{ notification.created_at|timesince }} مضت</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- Quick Actions -->
    <div class="card rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4">إجراءات سريعة</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="{% url 'pos:new_invoice' %}" class="bg-green-600 hover:bg-green-700 p-4 rounded-lg text-center transition-colors">
                <i class="fas fa-plus text-2xl mb-2"></i>
                <p class="text-sm font-medium">فاتورة جديدة</p>
            </a>
            
            <a href="{% url 'products:add' %}" class="bg-blue-600 hover:bg-blue-700 p-4 rounded-lg text-center transition-colors">
                <i class="fas fa-box text-2xl mb-2"></i>
                <p class="text-sm font-medium">إضافة منتج</p>
            </a>
            
            <a href="{% url 'pos:customer_add' %}" class="bg-purple-600 hover:bg-purple-700 p-4 rounded-lg text-center transition-colors">
                <i class="fas fa-user-plus text-2xl mb-2"></i>
                <p class="text-sm font-medium">عميل جديد</p>
            </a>
            
            <a href="{% url 'reports:financial' %}" class="bg-orange-600 hover:bg-orange-700 p-4 rounded-lg text-center transition-colors">
                <i class="fas fa-chart-bar text-2xl mb-2"></i>
                <p class="text-sm font-medium">التقارير</p>
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
</script>
{% endblock %}
