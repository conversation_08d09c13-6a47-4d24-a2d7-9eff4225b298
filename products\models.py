from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Category(models.Model):
    """
    فئات المنتجات
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name='اسم الفئة'
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='الوصف'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'فئة'
        verbose_name_plural = 'الفئات'

    def __str__(self):
        return self.name

class Product(models.Model):
    """
    نموذج المنتجات
    """
    name = models.CharField(
        max_length=200,
        verbose_name='اسم المنتج'
    )
    category = models.ForeignKey(
        Category,
        on_delete=models.CASCADE,
        related_name='products',
        verbose_name='الفئة'
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='الوصف'
    )
    sku = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='رمز المنتج'
    )
    purchase_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='سعر الشراء'
    )
    selling_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='سعر البيع'
    )
    quantity_in_stock = models.PositiveIntegerField(
        default=0,
        verbose_name='الكمية في المخزون'
    )
    minimum_stock_level = models.PositiveIntegerField(
        default=3,
        verbose_name='الحد الأدنى للمخزون'
    )
    image = models.ImageField(
        upload_to='products/',
        blank=True,
        null=True,
        verbose_name='صورة المنتج'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='أنشئ بواسطة'
    )

    class Meta:
        verbose_name = 'منتج'
        verbose_name_plural = 'المنتجات'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.sku}"

    @property
    def is_low_stock(self):
        """التحقق من انخفاض المخزون"""
        return self.quantity_in_stock <= self.minimum_stock_level

    @property
    def profit_margin(self):
        """حساب هامش الربح"""
        if self.purchase_price > 0:
            return ((self.selling_price - self.purchase_price) / self.purchase_price) * 100
        return 0
